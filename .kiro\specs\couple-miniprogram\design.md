# 设计文档

## 概述

情侣小程序是一个基于uni-app框架开发的跨平台应用，主要面向微信小程序平台。该应用采用Vue 3 + TypeScript技术栈，结合uniCloud云开发服务，为情侣用户提供私密、安全、功能丰富的数字化情感管理平台。

## 架构设计

### 整体架构

```mermaid
graph TB
    A[前端 - uni-app] --> B[云函数 - uniCloud]
    B --> C[云数据库 - MongoDB]
    B --> D[云存储 - 图片/文件]
    A --> E[第三方服务]
    E --> F[微信登录]
    E --> G[推送通知]
    
    subgraph "前端架构"
        H[页面层 Pages]
        I[组件层 Components]
        J[状态管理 Pinia]
        K[工具层 Utils]
        L[API层 Services]
    end
```

### 技术栈选择

- **前端框架**: uni-app + Vue 3 + JavaScript
- **状态管理**: Vuex 或简单的全局状态管理
- **UI组件**: uni-ui + 自定义组件
- **后端服务**: uniCloud (阿里云版)
- **数据库**: MongoDB (云数据库)
- **文件存储**: uniCloud云存储
- **实时通信**: uniCloud实时数据推送

## 组件和接口设计

### 页面结构设计

```
pages/
├── auth/                    # 认证相关页面
│   ├── login/              # 登录页面
│   ├── register/           # 注册页面
│   └── pairing/            # 配对页面
├── home/                   # 主页
│   └── index/              # 情侣主页
├── album/                  # 相册模块
│   ├── index/              # 相册列表
│   ├── detail/             # 照片详情
│   └── upload/             # 照片上传
├── chat/                   # 聊天模块
│   └── index/              # 聊天页面
├── anniversary/            # 纪念日模块
│   ├── index/              # 纪念日列表
│   └── add/                # 添加纪念日
├── diary/                  # 心情日记
│   ├── index/              # 日记列表
│   ├── write/              # 写日记
│   └── detail/             # 日记详情
├── wishlist/               # 愿望清单
│   ├── index/              # 愿望列表
│   └── add/                # 添加愿望
├── period/                 # 生理期管理
│   ├── index/              # 生理期日历
│   └── record/             # 记录页面
└── settings/               # 设置页面
    ├── index/              # 设置首页
    ├── profile/            # 个人资料
    ├── notification/       # 通知设置
    └── privacy/            # 隐私设置
```

### 核心组件设计

#### 1. 用户认证组件
```javascript
// components/auth/LoginForm.vue
// Props: mode ('login' | 'register')
// Events: @success, @error
export default {
  props: {
    mode: {
      type: String,
      default: 'login'
    }
  },
  emits: ['success', 'error']
}
```

#### 2. 情侣卡片组件
```javascript
// components/couple/CoupleCard.vue
export default {
  props: {
    coupleInfo: Object, // 包含用户信息和开始日期
    showDays: Boolean,
    showAnniversary: Boolean
  }
}
```

#### 3. 照片网格组件
```javascript
// components/album/PhotoGrid.vue
export default {
  props: {
    photos: Array,
    columns: {
      type: Number,
      default: 3
    },
    showDate: Boolean
  },
  emits: ['photoClick', 'photoLongPress']
}
```

#### 4. 聊天消息组件
```javascript
// components/chat/MessageItem.vue
export default {
  props: {
    message: Object, // 消息对象
    isOwn: Boolean,  // 是否是自己发送的
    showAvatar: Boolean
  }
}
```

### API接口设计

#### 云函数接口设计

```javascript
// 用户登录 - 云函数: user-login
{
  "phone": "13800138000",
  "password": "123456"
}

// 用户注册 - 云函数: user-register  
{
  "phone": "13800138000",
  "password": "123456",
  "nickname": "小明"
}

// 情侣配对 - 云函数: couple-pair
{
  "pairCode": "ABC123"
}

// 获取相册列表 - 云函数: album-list
{
  "page": 1,
  "limit": 20
}

// 上传照片 - 云函数: album-upload
{
  "fileID": "cloud://xxx.jpg",
  "description": "美好的一天",
  "date": "2025-01-01"
}

// 发送消息 - 云函数: chat-send
{
  "content": "你好",
  "type": "text",
  "receiverId": "user123"
}
```

## 数据模型设计

## 数据库设计

### 数据库集合结构

#### 1. 用户集合 (uni-id-users)
```javascript
// 集合名: uni-id-users
{
  "_id": "user123",                    // 用户唯一标识ID
  "username": "13800138000",           // 用户名（手机号）
  "password": "加密后的密码",           // 登录密码（加密存储）
  "nickname": "小明",                  // 用户昵称
  "avatar": "cloud://avatar.jpg",      // 用户头像（云存储文件ID）
  "gender": "male",                    // 性别：male-男性，female-女性
  "birthday": "1995-01-01",            // 生日日期
  "coupleId": "couple123",             // 关联的情侣关系ID
  "register_date": 1640995200000,      // 注册时间戳
  "last_login_date": 1640995200000     // 最后登录时间戳
}
```

#### 2. 情侣关系集合 (couples)
```javascript
// 集合名: couples
{
  "_id": "couple123",                  // 情侣关系唯一标识ID
  "user1Id": "user123",                // 第一个用户ID
  "user2Id": "user456",                // 第二个用户ID
  "startDate": "2024-01-01",           // 恋爱开始日期
  "pairCode": "ABC123",                // 配对码（用于情侣配对）
  "status": "active",                  // 关系状态：active-活跃，inactive-非活跃
  "createTime": 1640995200000,         // 创建时间戳
  "updateTime": 1640995200000          // 更新时间戳
}
```

#### 3. 照片集合 (photos)
```javascript
// 集合名: photos
{
  "_id": "photo123",                   // 照片唯一标识ID
  "coupleId": "couple123",             // 所属情侣关系ID
  "uploaderId": "user123",             // 上传者用户ID
  "fileID": "cloud://photo.jpg",       // 原图云存储文件ID
  "thumbnailID": "cloud://thumb.jpg",  // 缩略图云存储文件ID
  "description": "美好的一天",          // 照片描述文字
  "shootDate": "2024-01-01",           // 拍摄日期
  "location": "北京市朝阳区",           // 拍摄地点
  "tags": ["旅行", "美食"],            // 照片标签数组
  "createTime": 1640995200000          // 上传时间戳
}
```

#### 4. 聊天消息集合 (chat_messages)
```javascript
// 集合名: chat_messages
{
  "_id": "msg123",                     // 消息唯一标识ID
  "coupleId": "couple123",             // 所属情侣关系ID
  "senderId": "user123",               // 发送者用户ID
  "receiverId": "user456",             // 接收者用户ID
  "content": "你好",                   // 消息内容
  "type": "text",                      // 消息类型：text-文字，image-图片，emoji-表情
  "status": "sent",                    // 消息状态：sent-已发送，delivered-已送达，read-已读
  "createTime": 1640995200000          // 发送时间戳
}
```

#### 5. 纪念日集合 (anniversaries)
```javascript
// 集合名: anniversaries
{
  "_id": "anni123",                    // 纪念日唯一标识ID
  "coupleId": "couple123",             // 所属情侣关系ID
  "title": "第一次约会",               // 纪念日标题
  "description": "在咖啡厅的美好时光", // 纪念日描述
  "date": "2024-02-14",                // 纪念日日期
  "type": "anniversary",               // 类型：anniversary-纪念日，birthday-生日，custom-自定义
  "isRecurring": true,                 // 是否每年重复提醒
  "reminderDays": [3, 1],              // 提前提醒天数数组（提前3天和1天）
  "createTime": 1640995200000          // 创建时间戳
}
```

#### 6. 心情日记集合 (diaries)
```javascript
// 集合名: diaries
{
  "_id": "diary123",                   // 日记唯一标识ID
  "coupleId": "couple123",             // 所属情侣关系ID
  "authorId": "user123",               // 作者用户ID
  "content": "今天心情很好",           // 日记内容
  "mood": "happy",                     // 心情状态：happy-开心，sad-难过，excited-兴奋，calm-平静，angry-生气，love-恋爱
  "isPrivate": false,                  // 是否私密（只有自己可见）
  "likes": ["user456"],                // 点赞用户ID数组
  "comments": [                        // 评论数组
    {
      "userId": "user456",             // 评论者用户ID
      "content": "我也很开心",         // 评论内容
      "createTime": 1640995200000      // 评论时间戳
    }
  ],
  "createTime": 1640995200000          // 创建时间戳
}
```

#### 7. 愿望清单集合 (wishlists)
```javascript
// 集合名: wishlists
{
  "_id": "wish123",                    // 愿望唯一标识ID
  "coupleId": "couple123",             // 所属情侣关系ID
  "title": "去日本旅行",               // 愿望标题
  "description": "想要一起去看樱花",   // 愿望详细描述
  "priority": "high",                  // 优先级：high-高，medium-中，low-低
  "status": "pending",                 // 状态：pending-待完成，completed-已完成
  "completedDate": null,               // 完成日期（完成时填写）
  "completedPhoto": null,              // 完成时的纪念照片（云存储文件ID）
  "createdBy": "user123",              // 创建者用户ID
  "createTime": 1640995200000          // 创建时间戳
}
```

#### 8. 生理期记录集合 (period_records)
```javascript
// 集合名: period_records
{
  "_id": "period123",                  // 生理期记录唯一标识ID
  "userId": "user456",                 // 女性用户ID
  "coupleId": "couple123",             // 所属情侣关系ID
  "startDate": "2024-01-01",           // 生理期开始日期
  "endDate": "2024-01-05",             // 生理期结束日期
  "cycleLength": 28,                   // 生理周期长度（天数）
  "symptoms": ["腹痛", "情绪波动"],    // 症状数组
  "painLevel": 3,                      // 疼痛等级（1-5级，1最轻5最重）
  "notes": "这次比较轻松",             // 备注说明
  "createTime": 1640995200000          // 记录创建时间戳
}
```

### 数据库索引设计

```javascript
// 用户集合索引
db.collection('uni-id-users').createIndex({"username": 1}) // 手机号唯一索引
db.collection('uni-id-users').createIndex({"coupleId": 1}) // 情侣ID索引

// 情侣关系集合索引  
db.collection('couples').createIndex({"pairCode": 1}) // 配对码唯一索引
db.collection('couples').createIndex({"user1Id": 1, "user2Id": 1}) // 用户组合索引

// 照片集合索引
db.collection('photos').createIndex({"coupleId": 1, "createTime": -1}) // 按时间倒序

// 聊天消息集合索引
db.collection('chat_messages').createIndex({"coupleId": 1, "createTime": -1}) // 聊天记录索引

// 纪念日集合索引
db.collection('anniversaries').createIndex({"coupleId": 1, "date": 1}) // 按日期索引

// 日记集合索引
db.collection('diaries').createIndex({"coupleId": 1, "createTime": -1}) // 按时间倒序

// 愿望清单集合索引
db.collection('wishlists').createIndex({"coupleId": 1, "status": 1}) // 按状态索引

// 生理期记录集合索引
db.collection('period_records').createIndex({"userId": 1, "startDate": -1}) // 按开始日期倒序
```

### 数据库权限设计

```javascript
// schema/couples.json - 情侣关系表权限
{
  "bsonType": "object",
  "permission": {
    "read": "doc.user1Id == auth.uid || doc.user2Id == auth.uid",
    "create": "auth.uid != null",
    "update": "doc.user1Id == auth.uid || doc.user2Id == auth.uid",
    "delete": false
  }
}

// schema/photos.json - 照片表权限
{
  "bsonType": "object", 
  "permission": {
    "read": "get(`database`).collection(`couples`).where({_id: doc.coupleId, $or: [{user1Id: auth.uid}, {user2Id: auth.uid}]}).count() > 0",
    "create": "auth.uid != null && doc.uploaderId == auth.uid",
    "update": "doc.uploaderId == auth.uid",
    "delete": "doc.uploaderId == auth.uid"
  }
}
```

## 错误处理

### 错误分类
1. **网络错误**: 请求超时、网络不可用
2. **认证错误**: 登录失效、权限不足
3. **业务错误**: 数据验证失败、操作不允许
4. **系统错误**: 服务器内部错误

### 错误处理策略
```typescript
// utils/errorHandler.ts
export class ErrorHandler {
  static handle(error: ApiError) {
    switch (error.code) {
      case 'NETWORK_ERROR':
        uni.showToast({ title: '网络连接失败', icon: 'none' })
        break
      case 'AUTH_EXPIRED':
        // 跳转到登录页面
        uni.redirectTo({ url: '/pages/auth/login/index' })
        break
      case 'VALIDATION_ERROR':
        uni.showToast({ title: error.message, icon: 'none' })
        break
      default:
        uni.showToast({ title: '操作失败，请重试', icon: 'none' })
    }
  }
}
```

## 测试策略

### 单元测试
- 使用 Jest 进行组件和工具函数测试
- 覆盖率目标: 80%以上
- 重点测试: 数据处理、状态管理、工具函数

### 集成测试
- 测试页面间的导航和数据传递
- 测试API接口的调用和响应处理
- 测试用户操作流程

### 端到端测试
- 使用自动化测试工具测试完整用户流程
- 测试关键功能: 登录配对、照片上传、消息发送

### 性能测试
- 页面加载时间监控
- 图片加载优化测试
- 内存使用情况监控

## 安全考虑

### 数据安全
1. **数据加密**: 敏感数据传输使用HTTPS
2. **数据脱敏**: 日志中不记录敏感信息
3. **访问控制**: 严格的权限验证机制

### 隐私保护
1. **数据隔离**: 不同情侣的数据完全隔离
2. **数据删除**: 提供数据删除功能
3. **隐私设置**: 用户可控制数据可见性

### 输入验证
```typescript
// utils/validator.ts
export class Validator {
  static validatePhone(phone: string): boolean {
    return /^1[3-9]\d{9}$/.test(phone)
  }
  
  static validatePassword(password: string): boolean {
    return password.length >= 6 && password.length <= 20
  }
  
  static sanitizeInput(input: string): string {
    return input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
  }
}
```