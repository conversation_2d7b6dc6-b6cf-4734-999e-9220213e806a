{"version": 3, "file": "user.js", "sources": ["pages/user/user.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci91c2VyLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 背景装饰 -->\r\n\t\t<view class=\"bg-decoration\">\r\n\t\t\t<view class=\"decoration-circle circle-1\"></view>\r\n\t\t\t<view class=\"decoration-circle circle-2\"></view>\r\n\t\t\t<view class=\"decoration-circle circle-3\"></view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 个人信息卡片 -->\r\n\t\t<view class=\"profile-section\">\r\n\t\t\t<view class=\"profile-card\">\r\n\t\t\t\t<view class=\"avatar-container\">\r\n\t\t\t\t\t<image :src=\"(userInfo && userInfo.avatarUrl) || defaultAvatar\" class=\"profile-avatar\" />\r\n\t\t\t\t\t<view class=\"avatar-border\"></view>\r\n\t\t\t\t\t<view class=\"online-status\" v-if=\"userInfo\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"profile-info\">\r\n\t\t\t\t\t<text class=\"profile-name\">{{ (userInfo && userInfo.nickName) || 'Hi, 游客' }}</text>\r\n\t\t\t\t\t<text class=\"profile-subtitle\" v-if=\"userInfo\">{{ userInfo.signature || '还没有个性签名~' }}</text>\r\n\t\t\t\t\t<text class=\"profile-subtitle\" v-else>点击登录，开启甜蜜之旅</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button class=\"login-btn\" @click=\"handleLogin\" v-if=\"!userInfo\">\r\n\t\t\t\t\t<text class=\"login-text\">微信登录</text>\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 情侣关系状态 -->\r\n\t\t<view class=\"relationship-card\" v-if=\"userInfo\">\r\n\t\t\t<view class=\"relationship-header\">\r\n\t\t\t\t<text class=\"relationship-title\">情侣关系</text>\r\n\t\t\t\t<text class=\"relationship-status\">{{ relationshipStatus }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"couple-avatars\">\r\n\t\t\t\t<view class=\"couple-item\">\r\n\t\t\t\t\t<image :src=\"userInfo.avatarUrl\" class=\"couple-avatar\" />\r\n\t\t\t\t\t<text class=\"couple-name\">{{ userInfo.nickName }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"heart-connector\">💕</view>\r\n\t\t\t\t<view class=\"couple-item\">\r\n\t\t\t\t\t<image :src=\"partnerInfo.avatar\" class=\"couple-avatar\" />\r\n\t\t\t\t\t<text class=\"couple-name\">{{ partnerInfo.name }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"relationship-days\">\r\n\t\t\t\t<text class=\"days-text\">在一起 {{ loveDays }} 天</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 数据统计 -->\r\n\t\t<view class=\"stats-section\">\r\n\t\t\t<text class=\"section-title\">我们的数据</text>\r\n\t\t\t<view class=\"stats-grid\">\r\n\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t<text class=\"stat-number\">{{ stats.photos }}</text>\r\n\t\t\t\t\t<text class=\"stat-label\">照片</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t<text class=\"stat-number\">{{ stats.diaries }}</text>\r\n\t\t\t\t\t<text class=\"stat-label\">日记</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t<text class=\"stat-number\">{{ stats.anniversaries }}</text>\r\n\t\t\t\t\t<text class=\"stat-label\">纪念日</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t<text class=\"stat-number\">{{ stats.messages }}</text>\r\n\t\t\t\t\t<text class=\"stat-label\">消息</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 功能菜单 -->\r\n\t\t<view class=\"menu-section\">\r\n\t\t\t<view class=\"menu-item\" @tap=\"editProfile\">\r\n\t\t\t\t<view class=\"menu-icon\">👤</view>\r\n\t\t\t\t<view class=\"menu-content\">\r\n\t\t\t\t\t<text class=\"menu-title\">编辑资料</text>\r\n\t\t\t\t\t<text class=\"menu-desc\">修改个人信息</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"menu-arrow\">→</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"menu-item\" @tap=\"goToAnniversary\">\r\n\t\t\t\t<view class=\"menu-icon\">🎉</view>\r\n\t\t\t\t<view class=\"menu-content\">\r\n\t\t\t\t\t<text class=\"menu-title\">纪念日管理</text>\r\n\t\t\t\t\t<text class=\"menu-desc\">添加重要日子</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"menu-arrow\">→</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"menu-item\" @tap=\"goToSettings\">\r\n\t\t\t\t<view class=\"menu-icon\">⚙️</view>\r\n\t\t\t\t<view class=\"menu-content\">\r\n\t\t\t\t\t<text class=\"menu-title\">设置</text>\r\n\t\t\t\t\t<text class=\"menu-desc\">隐私与通知</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"menu-arrow\">→</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"menu-item\" @tap=\"goToHelp\">\r\n\t\t\t\t<view class=\"menu-icon\">❓</view>\r\n\t\t\t\t<view class=\"menu-content\">\r\n\t\t\t\t\t<text class=\"menu-title\">帮助与反馈</text>\r\n\t\t\t\t\t<text class=\"menu-desc\">使用指南</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"menu-arrow\">→</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 退出登录 -->\r\n\t\t<view class=\"logout-section\" v-if=\"userInfo\">\r\n\t\t\t<button class=\"logout-btn\" @click=\"handleLogout\">\r\n\t\t\t\t<text class=\"logout-text\">退出登录</text>\r\n\t\t\t</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tuserInfo: null,\r\n\t\t\t\tdefaultAvatar: '/static/default-avatar.png',\r\n\t\t\t\trelationshipStatus: '已连接',\r\n\t\t\t\tloveDays: 365,\r\n\t\t\t\tpartnerInfo: {\r\n\t\t\t\t\tavatar: '/static/avatar2.png',\r\n\t\t\t\t\tname: '小帅'\r\n\t\t\t\t},\r\n\t\t\t\tstats: {\r\n\t\t\t\t\tphotos: 128,\r\n\t\t\t\t\tdiaries: 45,\r\n\t\t\t\t\tanniversaries: 8,\r\n\t\t\t\t\tmessages: 1024\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 登录功能\r\n\t\t\tasync handleLogin() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 1. 获取用户信息\r\n\t\t\t\t\tconst userProfileRes = await new Promise((resolve, reject) => {\r\n\t\t\t\t\t\tuni.getUserProfile({\r\n\t\t\t\t\t\t\tdesc: '登录后可以同步数据',\r\n\t\t\t\t\t\t\tsuccess: resolve,\r\n\t\t\t\t\t\t\tfail: reject\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// 修复：变量名应为 userProfileRes（之前写的 res 未定义）\r\n\t\t\t\t\tconst userInfor = userProfileRes.userInfo;\r\n\t\t\t\t\tconsole.log(\"用户信息\", userInfor);\r\n\r\n\t\t\t\t\t// 2. 获取临时code\r\n\t\t\t\t\tconst loginRes = await new Promise((resolve, reject) => {\r\n\t\t\t\t\t\tuni.login({\r\n\t\t\t\t\t\t\tprovider: \"weixin\",\r\n\t\t\t\t\t\t\tsuccess: resolve,\r\n\t\t\t\t\t\t\tfail: reject\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t});\r\n\t\t\t\t\tconst code = loginRes.code;\r\n\t\t\t\t\tif (!code) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"获取code失败\",\r\n\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 3. 用code换取openid\r\n\t\t\t\t\tconst openidRes = await uniCloud.callFunction({\r\n\t\t\t\t\t\tname: \"getOpenId\",\r\n\t\t\t\t\t\tdata: {\r\n\t\t\t\t\t\t\tcode\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\tconst openid = openidRes.result.openid;\r\n\t\t\t\t\tif (!openid) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"获取openid失败\",\r\n\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t1\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 4. 保存用户信息到云函数\r\n\t\t\t\t\tconst saveRes = await uniCloud.callFunction({\r\n\t\t\t\t\t\tname: \"save-user-infor\",\r\n\t\t\t\t\t\tdata: {\r\n\t\t\t\t\t\t\tnickname: userInfor.nickName,\r\n\t\t\t\t\t\t\tavatarUrl: userInfor.avatarUrl,\r\n\t\t\t\t\t\t\tgender: userInfor.gender,\r\n\t\t\t\t\t\t\topenid: openid\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\tconsole.log(\"保存用户结果\", saveRes.result);\r\n\r\n\t\t\t\t\t// 5. 登录成功：更新页面数据和本地存储\r\n\t\t\t\t\tthis.userInfo = {\r\n\t\t\t\t\t\t...userInfor,\r\n\t\t\t\t\t\topenid\r\n\t\t\t\t\t};\r\n\t\t\t\t\tuni.setStorageSync('userInfo', this.userInfo);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '登录成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t} catch (err) { // 修复：拼写错误 chatch → catch，且放在 try 对应的代码块内\r\n\t\t\t\t\tconsole.error('登录错误:', err);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '登录失败，请重试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\teditProfile() {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '编辑个人资料功能待开发',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgoToAnniversary() {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '纪念日管理功能待开发',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgoToSettings() {\r\n\t\t\t\tuni.navigateTo({ url: '/pages/setting/setting' });\r\n\t\t\t},\r\n\t\t\tgoToHelp() {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '帮助与反馈功能待开发',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\thandleLogout(){\r\n\t\t\t\tconsole.log(\"点击退出了\");\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle:\"确认退出\",\r\n\t\t\t\t\tcontent:'确定要退出登录吗？',\r\n\t\t\t\t\t   cancelText: '取消',\r\n\t\t\t\t\t        confirmText: '退出',\r\n\t\t\t\t\t\t\tsuccess:(res)=>{\r\n\t\t\t\t\t\t\t\tif(res.confirm){\r\n\t\t\t\t\t\t\t\t\tuni.removeStorageSync(\"userInfo\")\r\n\t\t\t\t\t\t\t\t\tthis.userInfo=null\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t       uni.showToast({\r\n\t\t\t\t\t\t\t\t\t              title: '已退出登录',\r\n\t\t\t\t\t\t\t\t\t              icon: 'success',\r\n\t\t\t\t\t\t\t\t\t              duration: 2000\r\n\t\t\t\t\t\t\t\t\t            });\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t},fail: (err) => {\r\n\t\t\t\t\t\t\t\tconsole.log(\"退出登录出错\")\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style scoped>\r\n\t.container {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);\r\n\t\tpadding: 30rpx;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t/* 背景装饰 */\r\n\t.bg-decoration {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tpointer-events: none;\r\n\t\tz-index: 1;\r\n\t}\r\n\r\n\t.decoration-circle {\r\n\t\tposition: absolute;\r\n\t\tborder-radius: 50%;\r\n\t\tbackground: rgba(255, 255, 255, 0.1);\r\n\t\tanimation: float 6s ease-in-out infinite;\r\n\t}\r\n\r\n\t.circle-1 {\r\n\t\twidth: 200rpx;\r\n\t\theight: 200rpx;\r\n\t\ttop: 10%;\r\n\t\tright: -50rpx;\r\n\t\tanimation-delay: 0s;\r\n\t}\r\n\r\n\t.circle-2 {\r\n\t\twidth: 150rpx;\r\n\t\theight: 150rpx;\r\n\t\ttop: 60%;\r\n\t\tleft: -30rpx;\r\n\t\tanimation-delay: 2s;\r\n\t}\r\n\r\n\t.circle-3 {\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\ttop: 30%;\r\n\t\tleft: 50%;\r\n\t\tanimation-delay: 4s;\r\n\t}\r\n\r\n\t@keyframes float {\r\n\r\n\t\t0%,\r\n\t\t100% {\r\n\t\t\ttransform: translateY(0px) rotate(0deg);\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\ttransform: translateY(-20rpx) rotate(180deg);\r\n\t\t}\r\n\t}\r\n\r\n\t/* 个人信息区域 */\r\n\t.profile-section {\r\n\t\tposition: relative;\r\n\t\tz-index: 2;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.profile-card {\r\n\t\tbackground: rgba(255, 255, 255, 0.95);\r\n\t\tborder-radius: 30rpx;\r\n\t\tpadding: 40rpx;\r\n\t\ttext-align: center;\r\n\t\tbox-shadow: 0 15rpx 35rpx rgba(255, 105, 180, 0.3);\r\n\t\tbackdrop-filter: blur(15rpx);\r\n\t\tanimation: slideUp 0.8s ease-out;\r\n\t}\r\n\r\n\t.avatar-container {\r\n\t\tposition: relative;\r\n\t\tdisplay: inline-block;\r\n\t\tmargin-bottom: 25rpx;\r\n\t}\r\n\r\n\t.profile-avatar {\r\n\t\twidth: 160rpx;\r\n\t\theight: 160rpx;\r\n\t\tborder-radius: 80rpx;\r\n\t\tborder: 4rpx solid #ff69b4;\r\n\t\tposition: relative;\r\n\t\tz-index: 2;\r\n\t}\r\n\r\n\t.avatar-border {\r\n\t\tposition: absolute;\r\n\t\ttop: -10rpx;\r\n\t\tleft: -10rpx;\r\n\t\twidth: 180rpx;\r\n\t\theight: 180rpx;\r\n\t\tborder: 2rpx solid rgba(255, 105, 180, 0.3);\r\n\t\tborder-radius: 90rpx;\r\n\t\tanimation: rotate 4s linear infinite;\r\n\t}\r\n\r\n\t.online-status {\r\n\t\tposition: absolute;\r\n\t\tbottom: 10rpx;\r\n\t\tright: 10rpx;\r\n\t\twidth: 24rpx;\r\n\t\theight: 24rpx;\r\n\t\tbackground: #4caf50;\r\n\t\tborder: 3rpx solid white;\r\n\t\tborder-radius: 50%;\r\n\t\tz-index: 3;\r\n\t}\r\n\r\n\t.profile-info {\r\n\t\tmargin-bottom: 25rpx;\r\n\t}\r\n\r\n\t.profile-name {\r\n\t\tfont-size: 36rpx;\r\n\t\tcolor: #ff1493;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.profile-subtitle {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #ff69b4;\r\n\t\topacity: 0.8;\r\n\t}\r\n\r\n\t.login-btn {\r\n\t\tbackground: linear-gradient(135deg, #ff69b4, #ff1493);\r\n\t\tborder: none;\r\n\t\tborder-radius: 25rpx;\r\n\t\tpadding: 20rpx 40rpx;\r\n\t\tbox-shadow: 0 8rpx 20rpx rgba(255, 20, 147, 0.3);\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.login-btn:active {\r\n\t\ttransform: scale(0.95);\r\n\t\tbox-shadow: 0 4rpx 15rpx rgba(255, 20, 147, 0.4);\r\n\t}\r\n\r\n\t.login-text {\r\n\t\tcolor: white;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 600;\r\n\t}\r\n\r\n\t/* 情侣关系卡片 */\r\n\t.relationship-card {\r\n\t\tbackground: rgba(255, 255, 255, 0.9);\r\n\t\tborder-radius: 25rpx;\r\n\t\tpadding: 35rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tbox-shadow: 0 10rpx 30rpx rgba(255, 105, 180, 0.25);\r\n\t\tbackdrop-filter: blur(10rpx);\r\n\t\tposition: relative;\r\n\t\tz-index: 2;\r\n\t\tanimation: slideUp 0.8s ease-out 0.2s both;\r\n\t}\r\n\r\n\t.relationship-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 25rpx;\r\n\t}\r\n\r\n\t.relationship-title {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #ff1493;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.relationship-status {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #4caf50;\r\n\t\tbackground: rgba(76, 175, 80, 0.1);\r\n\t\tpadding: 8rpx 16rpx;\r\n\t\tborder-radius: 15rpx;\r\n\t}\r\n\r\n\t.couple-avatars {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.couple-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.couple-avatar {\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\tborder: 3rpx solid #ff69b4;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.couple-name {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #ff1493;\r\n\t\tfont-weight: 600;\r\n\t}\r\n\r\n\t.heart-connector {\r\n\t\tfont-size: 40rpx;\r\n\t\tanimation: pulse 2s infinite;\r\n\t}\r\n\r\n\t.relationship-days {\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.days-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #ff69b4;\r\n\t\topacity: 0.8;\r\n\t}\r\n\r\n\t/* 数据统计 */\r\n\t.stats-section {\r\n\t\tbackground: rgba(255, 255, 255, 0.9);\r\n\t\tborder-radius: 25rpx;\r\n\t\tpadding: 35rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tbox-shadow: 0 10rpx 30rpx rgba(255, 105, 180, 0.25);\r\n\t\tbackdrop-filter: blur(10rpx);\r\n\t\tposition: relative;\r\n\t\tz-index: 2;\r\n\t\tanimation: slideUp 0.8s ease-out 0.4s both;\r\n\t}\r\n\r\n\t.section-title {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #ff1493;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 25rpx;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.stats-grid {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.stat-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tflex: 1;\r\n\t\tpadding: 20rpx 10rpx;\r\n\t\tborder-radius: 15rpx;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.stat-item:active {\r\n\t\tbackground: rgba(255, 105, 180, 0.1);\r\n\t\ttransform: scale(0.95);\r\n\t}\r\n\r\n\t.stat-number {\r\n\t\tfont-size: 36rpx;\r\n\t\tcolor: #ff1493;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.stat-label {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #ff69b4;\r\n\t\topacity: 0.8;\r\n\t}\r\n\r\n\t/* 功能菜单 */\r\n\t.menu-section {\r\n\t\tbackground: rgba(255, 255, 255, 0.9);\r\n\t\tborder-radius: 25rpx;\r\n\t\tpadding: 20rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tbox-shadow: 0 10rpx 30rpx rgba(255, 105, 180, 0.25);\r\n\t\tbackdrop-filter: blur(10rpx);\r\n\t\tposition: relative;\r\n\t\tz-index: 2;\r\n\t\tanimation: slideUp 0.8s ease-out 0.6s both;\r\n\t}\r\n\r\n\t.menu-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 25rpx 20rpx;\r\n\t\tborder-radius: 15rpx;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.menu-item:last-child {\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n\r\n\t.menu-item:active {\r\n\t\tbackground: rgba(255, 105, 180, 0.1);\r\n\t\ttransform: scale(0.98);\r\n\t}\r\n\r\n\t.menu-item::before {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: -100%;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\r\n\t\ttransition: left 0.5s ease;\r\n\t}\r\n\r\n\t.menu-item:active::before {\r\n\t\tleft: 100%;\r\n\t}\r\n\r\n\t.menu-icon {\r\n\t\tfont-size: 40rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t\twidth: 60rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.menu-content {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.menu-title {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #ff1493;\r\n\t\tfont-weight: 600;\r\n\t\tmargin-bottom: 5rpx;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.menu-desc {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #ff69b4;\r\n\t\topacity: 0.7;\r\n\t}\r\n\r\n\t.menu-arrow {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #ff69b4;\r\n\t\topacity: 0.6;\r\n\t}\r\n\r\n\t/* 退出登录 */\r\n\t.logout-section {\r\n\t\tposition: relative;\r\n\t\tz-index: 2;\r\n\t\tanimation: slideUp 0.8s ease-out 0.8s both;\r\n\t}\r\n\r\n\t.logout-btn {\r\n\t\twidth: 100%;\r\n\t\tbackground: rgba(255, 255, 255, 0.9);\r\n\t\tborder: 2rpx solid #ff69b4;\r\n\t\tborder-radius: 25rpx;\r\n\t\tpadding: 25rpx;\r\n\t\tbox-shadow: 0 8rpx 20rpx rgba(255, 105, 180, 0.2);\r\n\t\tbackdrop-filter: blur(10rpx);\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.logout-btn:active {\r\n\t\ttransform: scale(0.98);\r\n\t\tbackground: rgba(255, 105, 180, 0.1);\r\n\t}\r\n\r\n\t.logout-text {\r\n\t\tcolor: #ff1493;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 600;\r\n\t}\r\n\r\n\t/* 动画效果 */\r\n\t@keyframes slideUp {\r\n\t\t0% {\r\n\t\t\ttransform: translateY(50rpx);\r\n\t\t\topacity: 0;\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\ttransform: translateY(0);\r\n\t\t\topacity: 1;\r\n\t\t}\r\n\t}\r\n\r\n\t@keyframes rotate {\r\n\t\t0% {\r\n\t\t\ttransform: rotate(0deg);\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\ttransform: rotate(360deg);\r\n\t\t}\r\n\t}\r\n\r\n\t@keyframes pulse {\r\n\r\n\t\t0%,\r\n\t\t100% {\r\n\t\t\ttransform: scale(1);\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\ttransform: scale(1.1);\r\n\t\t}\r\n\t}\r\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/auglar/text/pages/user/user.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "uniCloud"], "mappings": ";;AAuHC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,UAAU;AAAA,MACV,eAAe;AAAA,MACf,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,aAAa;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACN;AAAA,MACD,OAAO;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,eAAe;AAAA,QACf,UAAU;AAAA,MACX;AAAA;EAED;AAAA,EACD,SAAS;AAAA;AAAA,IAER,MAAM,cAAc;AACnB,UAAI;AAEH,cAAM,iBAAiB,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7DA,wBAAAA,MAAI,eAAe;AAAA,YAClB,MAAM;AAAA,YACN,SAAS;AAAA,YACT,MAAM;AAAA,UACP,CAAC;AAAA,QACF,CAAC;AAED,cAAM,YAAY,eAAe;AACjCA,sBAAA,MAAA,MAAA,OAAA,8BAAY,QAAQ,SAAS;AAG7B,cAAM,WAAW,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvDA,wBAAAA,MAAI,MAAM;AAAA,YACT,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,WACN;AAAA,QACF,CAAC;AACD,cAAM,OAAO,SAAS;AACtB,YAAI,CAAC,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AACD;AAAA,QACD;AAGA,cAAM,YAAY,MAAMC,cAAQ,GAAC,aAAa;AAAA,UAC7C,MAAM;AAAA,UACN,MAAM;AAAA,YACL;AAAA,UACD;AAAA,QACD,CAAC;AACD,cAAM,SAAS,UAAU,OAAO;AAChC,YAAI,CAAC,QAAQ;AACZD,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AACD;AACA;AAAA,QACD;AAEA,cAAM,UAAU,MAAMC,cAAQ,GAAC,aAAa;AAAA,UAC3C,MAAM;AAAA,UACN,MAAM;AAAA,YACL,UAAU,UAAU;AAAA,YACpB,WAAW,UAAU;AAAA,YACrB,QAAQ,UAAU;AAAA,YAClB;AAAA,UACD;AAAA,QACD,CAAC;AACDD,sBAAY,MAAA,MAAA,OAAA,8BAAA,UAAU,QAAQ,MAAM;AAGpC,aAAK,WAAW;AAAA,UACf,GAAG;AAAA,UACH;AAAA;AAEDA,sBAAAA,MAAI,eAAe,YAAY,KAAK,QAAQ;AAC5CA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MAEA,SAAO,KAAK;AACbA,sBAAc,MAAA,MAAA,SAAA,8BAAA,SAAS,GAAG;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IACD,cAAc;AACbA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA,IACD,kBAAkB;AACjBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA,IACD,eAAe;AACdA,oBAAAA,MAAI,WAAW,EAAE,KAAK,yBAA0B,CAAA;AAAA,IAChD;AAAA,IACD,WAAW;AACVA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA,IACD,eAAc;AACbA,oBAAAA,MAAA,MAAA,OAAA,8BAAY,OAAO;AACnBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAM;AAAA,QACN,SAAQ;AAAA,QACL,YAAY;AAAA,QACP,aAAa;AAAA,QACnB,SAAQ,CAAC,QAAM;AACd,cAAG,IAAI,SAAQ;AACdA,0BAAG,MAAC,kBAAkB,UAAU;AAChC,iBAAK,WAAS;AAEPA,0BAAAA,MAAI,UAAU;AAAA,cACP,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YACZ,CAAC;AAAA,UACd;AAAA,QACD;AAAA,QAAE,MAAM,CAAC,QAAQ;AAChBA,wBAAAA,MAAA,MAAA,OAAA,8BAAY,QAAQ;AAAA,QACrB;AAAA,OACF;AAAA,IACF;AAAA,EACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrQF,GAAG,WAAW,eAAe;"}