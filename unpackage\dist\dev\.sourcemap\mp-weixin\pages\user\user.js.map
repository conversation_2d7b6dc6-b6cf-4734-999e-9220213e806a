{"version": 3, "file": "user.js", "sources": ["pages/user/user.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci91c2VyLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 背景装饰 -->\n    <view class=\"bg-decoration\">\n      <view class=\"decoration-circle circle-1\"></view>\n      <view class=\"decoration-circle circle-2\"></view>\n      <view class=\"decoration-circle circle-3\"></view>\n    </view>\n\n    <!-- 个人信息卡片 -->\n    <view class=\"profile-section\">\n      <view class=\"profile-card\">\n        <view class=\"avatar-container\">\n          <image :src=\"(userInfo && userInfo.avatarUrl) || defaultAvatar\" class=\"profile-avatar\" />\n          <view class=\"avatar-border\"></view>\n          <view class=\"online-status\" v-if=\"userInfo\"></view>\n        </view>\n        <view class=\"profile-info\">\n          <text class=\"profile-name\">{{ (userInfo && userInfo.nickName) || 'Hi, 游客' }}</text>\n          <text class=\"profile-subtitle\" v-if=\"userInfo\">{{ userInfo.signature || '还没有个性签名~' }}</text>\n          <text class=\"profile-subtitle\" v-else>点击登录，开启甜蜜之旅</text>\n        </view>\n        <button class=\"login-btn\" @tap=\"handleLogin\" v-if=\"!userInfo\">\n          <text class=\"login-text\">微信登录</text>\n        </button>\n      </view>\n    </view>\n\n    <!-- 情侣关系状态 -->\n    <view class=\"relationship-card\" v-if=\"userInfo\">\n      <view class=\"relationship-header\">\n        <text class=\"relationship-title\">情侣关系</text>\n        <text class=\"relationship-status\">{{ relationshipStatus }}</text>\n      </view>\n      <view class=\"couple-avatars\">\n        <view class=\"couple-item\">\n          <image :src=\"userInfo.avatarUrl\" class=\"couple-avatar\" />\n          <text class=\"couple-name\">{{ userInfo.nickName }}</text>\n        </view>\n        <view class=\"heart-connector\">💕</view>\n        <view class=\"couple-item\">\n          <image :src=\"partnerInfo.avatar\" class=\"couple-avatar\" />\n          <text class=\"couple-name\">{{ partnerInfo.name }}</text>\n        </view>\n      </view>\n      <view class=\"relationship-days\">\n        <text class=\"days-text\">在一起 {{ loveDays }} 天</text>\n      </view>\n    </view>\n\n    <!-- 数据统计 -->\n    <view class=\"stats-section\">\n      <text class=\"section-title\">我们的数据</text>\n      <view class=\"stats-grid\">\n        <view class=\"stat-item\">\n          <text class=\"stat-number\">{{ stats.photos }}</text>\n          <text class=\"stat-label\">照片</text>\n        </view>\n        <view class=\"stat-item\">\n          <text class=\"stat-number\">{{ stats.diaries }}</text>\n          <text class=\"stat-label\">日记</text>\n        </view>\n        <view class=\"stat-item\">\n          <text class=\"stat-number\">{{ stats.anniversaries }}</text>\n          <text class=\"stat-label\">纪念日</text>\n        </view>\n        <view class=\"stat-item\">\n          <text class=\"stat-number\">{{ stats.messages }}</text>\n          <text class=\"stat-label\">消息</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 功能菜单 -->\n    <view class=\"menu-section\">\n      <view class=\"menu-item\" @tap=\"editProfile\">\n        <view class=\"menu-icon\">👤</view>\n        <view class=\"menu-content\">\n          <text class=\"menu-title\">编辑资料</text>\n          <text class=\"menu-desc\">修改个人信息</text>\n        </view>\n        <view class=\"menu-arrow\">→</view>\n      </view>\n      <view class=\"menu-item\" @tap=\"goToAnniversary\">\n        <view class=\"menu-icon\">🎉</view>\n        <view class=\"menu-content\">\n          <text class=\"menu-title\">纪念日管理</text>\n          <text class=\"menu-desc\">添加重要日子</text>\n        </view>\n        <view class=\"menu-arrow\">→</view>\n      </view>\n      <view class=\"menu-item\" @tap=\"goToSettings\">\n        <view class=\"menu-icon\">⚙️</view>\n        <view class=\"menu-content\">\n          <text class=\"menu-title\">设置</text>\n          <text class=\"menu-desc\">隐私与通知</text>\n        </view>\n        <view class=\"menu-arrow\">→</view>\n      </view>\n      <view class=\"menu-item\" @tap=\"goToHelp\">\n        <view class=\"menu-icon\">❓</view>\n        <view class=\"menu-content\">\n          <text class=\"menu-title\">帮助与反馈</text>\n          <text class=\"menu-desc\">使用指南</text>\n        </view>\n        <view class=\"menu-arrow\">→</view>\n      </view>\n    </view>\n\n    <!-- 退出登录 -->\n    <view class=\"logout-section\" v-if=\"userInfo\">\n      <button class=\"logout-btn\" @tap=\"handleLogout\">\n        <text class=\"logout-text\">退出登录</text>\n      </button>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      userInfo: null,\n      defaultAvatar: '/static/default-avatar.png',\n      relationshipStatus: '已连接',\n      loveDays: 365,\n      partnerInfo: {\n        avatar: '/static/avatar2.png',\n        name: '小帅'\n      },\n      stats: {\n        photos: 128,\n        diaries: 45,\n        anniversaries: 8,\n        messages: 1024\n      }\n    };\n  },\n  methods: {\n    handleLogin() {\n      // 模拟登录成功\n      this.userInfo = {\n        nickName: '小美',\n        avatarUrl: '/static/avatar1.png',\n        signature: '每天都要开心呀 💕'\n      };\n      uni.showToast({ title: '登录成功', icon: 'success' });\n    },\n    handleLogout() {\n      uni.showModal({\n        title: '确认退出',\n        content: '确定要退出登录吗？',\n        success: (res) => {\n          if (res.confirm) {\n            this.userInfo = null;\n            uni.showToast({ title: '已退出登录', icon: 'success' });\n          }\n        }\n      });\n    },\n    editProfile() {\n      uni.showToast({ title: '编辑个人资料功能待开发', icon: 'none' });\n    },\n    goToAnniversary() {\n      uni.showToast({ title: '纪念日管理功能待开发', icon: 'none' });\n    },\n    goToSettings() {\n      uni.showToast({ title: '设置功能待开发', icon: 'none' });\n    },\n    goToHelp() {\n      uni.showToast({ title: '帮助与反馈功能待开发', icon: 'none' });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);\n  padding: 30rpx;\n  position: relative;\n  overflow: hidden;\n}\n\n/* 背景装饰 */\n.bg-decoration {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n  z-index: 1;\n}\n\n.decoration-circle {\n  position: absolute;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.1);\n  animation: float 6s ease-in-out infinite;\n}\n\n.circle-1 {\n  width: 200rpx;\n  height: 200rpx;\n  top: 10%;\n  right: -50rpx;\n  animation-delay: 0s;\n}\n\n.circle-2 {\n  width: 150rpx;\n  height: 150rpx;\n  top: 60%;\n  left: -30rpx;\n  animation-delay: 2s;\n}\n\n.circle-3 {\n  width: 100rpx;\n  height: 100rpx;\n  top: 30%;\n  left: 50%;\n  animation-delay: 4s;\n}\n\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px) rotate(0deg);\n  }\n  50% {\n    transform: translateY(-20rpx) rotate(180deg);\n  }\n}\n\n/* 个人信息区域 */\n.profile-section {\n  position: relative;\n  z-index: 2;\n  margin-bottom: 30rpx;\n}\n\n.profile-card {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 30rpx;\n  padding: 40rpx;\n  text-align: center;\n  box-shadow: 0 15rpx 35rpx rgba(255, 105, 180, 0.3);\n  backdrop-filter: blur(15rpx);\n  animation: slideUp 0.8s ease-out;\n}\n\n.avatar-container {\n  position: relative;\n  display: inline-block;\n  margin-bottom: 25rpx;\n}\n\n.profile-avatar {\n  width: 160rpx;\n  height: 160rpx;\n  border-radius: 80rpx;\n  border: 4rpx solid #ff69b4;\n  position: relative;\n  z-index: 2;\n}\n\n.avatar-border {\n  position: absolute;\n  top: -10rpx;\n  left: -10rpx;\n  width: 180rpx;\n  height: 180rpx;\n  border: 2rpx solid rgba(255, 105, 180, 0.3);\n  border-radius: 90rpx;\n  animation: rotate 4s linear infinite;\n}\n\n.online-status {\n  position: absolute;\n  bottom: 10rpx;\n  right: 10rpx;\n  width: 24rpx;\n  height: 24rpx;\n  background: #4caf50;\n  border: 3rpx solid white;\n  border-radius: 50%;\n  z-index: 3;\n}\n\n.profile-info {\n  margin-bottom: 25rpx;\n}\n\n.profile-name {\n  font-size: 36rpx;\n  color: #ff1493;\n  font-weight: bold;\n  margin-bottom: 10rpx;\n  display: block;\n}\n\n.profile-subtitle {\n  font-size: 24rpx;\n  color: #ff69b4;\n  opacity: 0.8;\n}\n\n.login-btn {\n  background: linear-gradient(135deg, #ff69b4, #ff1493);\n  border: none;\n  border-radius: 25rpx;\n  padding: 20rpx 40rpx;\n  box-shadow: 0 8rpx 20rpx rgba(255, 20, 147, 0.3);\n  transition: all 0.3s ease;\n}\n\n.login-btn:active {\n  transform: scale(0.95);\n  box-shadow: 0 4rpx 15rpx rgba(255, 20, 147, 0.4);\n}\n\n.login-text {\n  color: white;\n  font-size: 28rpx;\n  font-weight: 600;\n}\n\n/* 情侣关系卡片 */\n.relationship-card {\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 25rpx;\n  padding: 35rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 10rpx 30rpx rgba(255, 105, 180, 0.25);\n  backdrop-filter: blur(10rpx);\n  position: relative;\n  z-index: 2;\n  animation: slideUp 0.8s ease-out 0.2s both;\n}\n\n.relationship-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 25rpx;\n}\n\n.relationship-title {\n  font-size: 30rpx;\n  color: #ff1493;\n  font-weight: bold;\n}\n\n.relationship-status {\n  font-size: 22rpx;\n  color: #4caf50;\n  background: rgba(76, 175, 80, 0.1);\n  padding: 8rpx 16rpx;\n  border-radius: 15rpx;\n}\n\n.couple-avatars {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 20rpx;\n}\n\n.couple-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  flex: 1;\n}\n\n.couple-avatar {\n  width: 100rpx;\n  height: 100rpx;\n  border-radius: 50rpx;\n  border: 3rpx solid #ff69b4;\n  margin-bottom: 10rpx;\n}\n\n.couple-name {\n  font-size: 22rpx;\n  color: #ff1493;\n  font-weight: 600;\n}\n\n.heart-connector {\n  font-size: 40rpx;\n  animation: pulse 2s infinite;\n}\n\n.relationship-days {\n  text-align: center;\n}\n\n.days-text {\n  font-size: 24rpx;\n  color: #ff69b4;\n  opacity: 0.8;\n}\n\n/* 数据统计 */\n.stats-section {\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 25rpx;\n  padding: 35rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 10rpx 30rpx rgba(255, 105, 180, 0.25);\n  backdrop-filter: blur(10rpx);\n  position: relative;\n  z-index: 2;\n  animation: slideUp 0.8s ease-out 0.4s both;\n}\n\n.section-title {\n  font-size: 30rpx;\n  color: #ff1493;\n  font-weight: bold;\n  margin-bottom: 25rpx;\n  display: block;\n}\n\n.stats-grid {\n  display: flex;\n  justify-content: space-between;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  flex: 1;\n  padding: 20rpx 10rpx;\n  border-radius: 15rpx;\n  transition: all 0.3s ease;\n}\n\n.stat-item:active {\n  background: rgba(255, 105, 180, 0.1);\n  transform: scale(0.95);\n}\n\n.stat-number {\n  font-size: 36rpx;\n  color: #ff1493;\n  font-weight: bold;\n  margin-bottom: 8rpx;\n}\n\n.stat-label {\n  font-size: 22rpx;\n  color: #ff69b4;\n  opacity: 0.8;\n}\n\n/* 功能菜单 */\n.menu-section {\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 25rpx;\n  padding: 20rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 10rpx 30rpx rgba(255, 105, 180, 0.25);\n  backdrop-filter: blur(10rpx);\n  position: relative;\n  z-index: 2;\n  animation: slideUp 0.8s ease-out 0.6s both;\n}\n\n.menu-item {\n  display: flex;\n  align-items: center;\n  padding: 25rpx 20rpx;\n  border-radius: 15rpx;\n  margin-bottom: 10rpx;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.menu-item:last-child {\n  margin-bottom: 0;\n}\n\n.menu-item:active {\n  background: rgba(255, 105, 180, 0.1);\n  transform: scale(0.98);\n}\n\n.menu-item::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n  transition: left 0.5s ease;\n}\n\n.menu-item:active::before {\n  left: 100%;\n}\n\n.menu-icon {\n  font-size: 40rpx;\n  margin-right: 20rpx;\n  width: 60rpx;\n  text-align: center;\n}\n\n.menu-content {\n  flex: 1;\n}\n\n.menu-title {\n  font-size: 28rpx;\n  color: #ff1493;\n  font-weight: 600;\n  margin-bottom: 5rpx;\n  display: block;\n}\n\n.menu-desc {\n  font-size: 22rpx;\n  color: #ff69b4;\n  opacity: 0.7;\n}\n\n.menu-arrow {\n  font-size: 24rpx;\n  color: #ff69b4;\n  opacity: 0.6;\n}\n\n/* 退出登录 */\n.logout-section {\n  position: relative;\n  z-index: 2;\n  animation: slideUp 0.8s ease-out 0.8s both;\n}\n\n.logout-btn {\n  width: 100%;\n  background: rgba(255, 255, 255, 0.9);\n  border: 2rpx solid #ff69b4;\n  border-radius: 25rpx;\n  padding: 25rpx;\n  box-shadow: 0 8rpx 20rpx rgba(255, 105, 180, 0.2);\n  backdrop-filter: blur(10rpx);\n  transition: all 0.3s ease;\n}\n\n.logout-btn:active {\n  transform: scale(0.98);\n  background: rgba(255, 105, 180, 0.1);\n}\n\n.logout-text {\n  color: #ff1493;\n  font-size: 28rpx;\n  font-weight: 600;\n}\n\n/* 动画效果 */\n@keyframes slideUp {\n  0% {\n    transform: translateY(50rpx);\n    opacity: 0;\n  }\n  100% {\n    transform: translateY(0);\n    opacity: 1;\n  }\n}\n\n@keyframes rotate {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.1);\n  }\n}\n</style>\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/auglar/text/pages/user/user.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAuHA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,UAAU;AAAA,MACV,eAAe;AAAA,MACf,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,aAAa;AAAA,QACX,QAAQ;AAAA,QACR,MAAM;AAAA,MACP;AAAA,MACD,OAAO;AAAA,QACL,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA;EAEH;AAAA,EACD,SAAS;AAAA,IACP,cAAc;AAEZ,WAAK,WAAW;AAAA,QACd,UAAU;AAAA,QACV,WAAW;AAAA,QACX,WAAW;AAAA;AAEbA,oBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,UAAQ,CAAG;AAAA,IACjD;AAAA,IACD,eAAe;AACbA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,iBAAK,WAAW;AAChBA,0BAAG,MAAC,UAAU,EAAE,OAAO,SAAS,MAAM,UAAQ,CAAG;AAAA,UACnD;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,cAAc;AACZA,oBAAG,MAAC,UAAU,EAAE,OAAO,eAAe,MAAM,OAAO,CAAC;AAAA,IACrD;AAAA,IACD,kBAAkB;AAChBA,oBAAG,MAAC,UAAU,EAAE,OAAO,cAAc,MAAM,OAAO,CAAC;AAAA,IACpD;AAAA,IACD,eAAe;AACbA,oBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,OAAK,CAAG;AAAA,IACjD;AAAA,IACD,WAAW;AACTA,oBAAG,MAAC,UAAU,EAAE,OAAO,cAAc,MAAM,OAAO,CAAC;AAAA,IACrD;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5KA,GAAG,WAAW,eAAe;"}