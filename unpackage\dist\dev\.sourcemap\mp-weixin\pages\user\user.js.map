{"version": 3, "file": "user.js", "sources": ["pages/user/user.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci91c2VyLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <view class=\"profile-card\">\n      <image :src=\"(userInfo && userInfo.avatarUrl) || defaultAvatar\" class=\"profile-avatar\" />\n      <text class=\"profile-name\">{{ (userInfo && userInfo.nickName) || 'Hi, 游客' }}</text>\n      <button @tap=\"handleLogin\" v-if=\"!userInfo\">微信登录</button>\n    </view>\n    <view class=\"action-buttons\">\n      <button @tap=\"goToAnniversary\">查看纪念日</button>\n      <button @tap=\"editProfile\">编辑个人资料</button>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      userInfo: null,\n      defaultAvatar: '/static/default-avatar.png'\n    };\n  },\n  methods: {\n    handleLogin() {\n      uni.showToast({ title: '登录功能待开发', icon: 'none' });\n    },\n    editProfile() {\n      uni.showToast({ title: '编辑个人资料功能待开发', icon: 'none' });\n    },\n    goToAnniversary() {\n      uni.showToast({ title: '查看纪念日功能待开发', icon: 'none' });\n    }\n  }\n};\n</script>\n\n<style scoped>\nbody {\n  font-family: \"Arial\", sans-serif;\n  background: linear-gradient(135deg, #e1bee7, #f3e5f5);\n  overflow: hidden;\n}\n\n.container {\n  padding: 50rpx;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.profile-card {\n  background: #fff;\n  width: 95%;\n  border-radius: 20rpx;\n  text-align: center;\n  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);\n  padding: 45rpx 25rpx;\n  transform: scale(1);\n  transition: transform 0.3s ease,\n              box-shadow 0.3s ease;\n}\n\n.profile-card:hover {\n  transform: scale(1.05);\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);\n}\n\n.profile-avatar {\n  width: 150rpx;\n  height: 150rpx;\n  border-radius: 75rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n  transition: box-shadow 0.3s ease;\n}\n\n.profile-avatar:hover {\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);\n}\n\n.profile-name {\n  font-size: 32rpx;\n  color: #6a1b9a;\n  margin-bottom: 25rpx;\n}\n\n.action-buttons {\n  margin-top: 40rpx;\n  width: 100%;\n  display: flex;\n  justify-content: space-evenly;\n}\n\n.action-buttons button {\n  width: 45%;\n  background: #ab47bc;\n  color: #fff;\n  border-radius: 30rpx;\n  padding: 20rpx;\n  border: none;\n  font-size: 28rpx;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);\n  transition: box-shadow 0.3s ease;\n}\n\n.action-buttons button:active {\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n</style>\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/auglar/text/pages/user/user.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAeA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,UAAU;AAAA,MACV,eAAe;AAAA;EAElB;AAAA,EACD,SAAS;AAAA,IACP,cAAc;AACZA,oBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,OAAK,CAAG;AAAA,IACjD;AAAA,IACD,cAAc;AACZA,oBAAG,MAAC,UAAU,EAAE,OAAO,eAAe,MAAM,OAAO,CAAC;AAAA,IACrD;AAAA,IACD,kBAAkB;AAChBA,oBAAG,MAAC,UAAU,EAAE,OAAO,cAAc,MAAM,OAAO,CAAC;AAAA,IACrD;AAAA,EACF;AACF;;;;;;;;;;;;;;AChCA,GAAG,WAAW,eAAe;"}