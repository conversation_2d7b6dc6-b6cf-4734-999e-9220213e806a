'use strict'
const db = uniCloud.database();
const userCollection = db.collection('users'); // 你的表名

exports.main = async (event, context) => {
  const { userInfo } = event;
  const uid = context.uid;

  if (!uid) {
    return {
      code: 401,
      msg: '未登录'
    };
  }

  // 构造字段
  const avatar = userInfo.avatarUrl || '';  // 微信头像
  const nickname = userInfo.nickName || '微信用户'; // 微信昵称
  const now = Date.now();

  const res = await userCollection.doc(uid).get();

  if (!res.data || res.data.length === 0) {
    // 用户不存在，创建新用户（可按需生成 username、password）
    await userCollection.add({
      _id: uid,  // 使用系统分配的 uid
      username: '', // 暂无手机号，后续绑定
      password: '', // 暂无密码
      nickname: nickname,
      avatar: avatar,
      gender: '', // 微信有 gender 字段你可以转成 male/female
      birthday: '', // 暂无
      coupleId: '',
      register_date: now,
      last_login_date: now
    });
  } else {
    // 用户已存在，更新头像昵称和登录时间
    await userCollection.doc(uid).update({
      nickname: nickname,
      avatar: avatar,
      last_login_date: now
    });
  }

  return {
    code: 0,
    msg: '用户信息保存成功',
    userInfo: {
      _id: uid,
      nickname: nickname,
      avatar: avatar
    }
  };
};
