'use strict'
const db = uniCloud.database();
const userCollection = db.collection('users'); // 表名

exports.main = async (event, context) => {
	const {
		nickname,
		avatarUrl,
		gender,
		openid
	} = event
	const userInDB = await userCollection.where({
		openid
	}).get()
	const currentTime = new Date();

	if (userInDB.affectedDocs > 0) {
		return {
			code: 1,
			msg: '用户存在，登录成功',
			userInfo: userInDB.data[0]
		}
	} else {
		const res = await userCollection.add({
			data: {
				openid,
				nickname: nickname || "微信用户",
				avatarUrl: avatarUrl,
				gender: gender === 1 ? "male" : (gender === 2 ? "female" : "unknown"),
				birthday: '',
				coupleId: '',
				register_date: currentTime,
				last_login_date: currentTime
			},

		})
		return {
			code: 0,
			msg: "新用户以注册",
			userId: res.id

		}
	}

};