{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页"
			}
		},
		{
			"path" : "pages/user/user",
			"style" : 
			{
				"navigationBarTitleText" : "我的"
			}
		}
	],
	"tabBar":{
		"list": [
			{
			"pagePath": "pages/index/index"	,
			"iconPath": "/static/tabbar/home.png",
			"selectedIconPath": "/static/tabbar/home-active.png",
				"text": "首页"
			},
			{
				"pagePath": "pages/user/user",
			"iconPath": "/static/tabbar/user.png",
			"selectedIconPath": "/static/tabbar/user-active.png",
			"text": "我的"
			}
		]
	},
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"uniIdRouter": {}
	
}
