'use strict';
const https=require('https')
exports.main = async (event, context) => {
	//event为客户端上传的参数
	console.log('event : ', event)
	const {code}=event
	if(!code){
		return{
			code:0,
			msg:'缺少code参数'
		}
	}
	const appid='wx24140a6b09cd2e19'
	const secret='efb724cb45dc7754bc15add3a877ab31'
	const url=`https://api.weixin.qq.com/sns/jscode2session?appid=${appid}&secret=${secret}&js_code=${code}&grant_type=authorization_code`
	return new Promise((resolve,reject)=>{
		https.get(url,(res)=>{
			let data=''
			res.on('data',chunk=>{
				data+=chunk
			})
			res.on('end',()=>{
				try{
					const result=JSON.parse(data)
					resolve(result)
				}catch(err){
					reject(err)
				}
			}).on('error',(e)=>{
				reject(e)
			})
		})
	})
	
	

};
