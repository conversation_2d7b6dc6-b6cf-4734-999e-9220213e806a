"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      loveDays: 365,
      loveStartDate: "2023-08-04",
      userAvatar: "/static/avatar1.png",
      partnerAvatar: "/static/avatar2.png",
      selectedMood: "",
      dailyQuote: "爱情不是寻找一个完美的人，而是学会用完美的眼光欣赏不完美的人 💕",
      moods: [
        { emoji: "😊", name: "开心" },
        { emoji: "😍", name: "甜蜜" },
        { emoji: "🥰", name: "幸福" },
        { emoji: "😌", name: "平静" },
        { emoji: "😔", name: "想念" }
      ]
    };
  },
  methods: {
    goToAlbum() {
      common_vendor.index.showToast({ title: "相册功能待开发", icon: "none" });
    },
    goToChat() {
      common_vendor.index.showToast({ title: "聊天功能待开发", icon: "none" });
    },
    goToAnniversary() {
      common_vendor.index.showToast({ title: "纪念日功能待开发", icon: "none" });
    },
    goToDiary() {
      common_vendor.index.showToast({ title: "日记功能待开发", icon: "none" });
    },
    selectMood(mood) {
      this.selectedMood = mood.name;
      common_vendor.index.showToast({ title: `选择了${mood.name}心情`, icon: "none" });
    },
    getParticleStyle(index) {
      const delay = Math.random() * 3;
      const duration = 3 + Math.random() * 2;
      const left = Math.random() * 100;
      return {
        left: left + "%",
        animationDelay: delay + "s",
        animationDuration: duration + "s"
      };
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f(20, (n, k0, i0) => {
      return {
        a: n,
        b: common_vendor.s($options.getParticleStyle(n))
      };
    }),
    b: common_vendor.t($data.dailyQuote),
    c: common_vendor.t($data.loveDays),
    d: $data.userAvatar,
    e: $data.partnerAvatar,
    f: common_vendor.t($data.loveStartDate),
    g: common_vendor.o((...args) => $options.goToAlbum && $options.goToAlbum(...args)),
    h: common_vendor.o((...args) => $options.goToChat && $options.goToChat(...args)),
    i: common_vendor.o((...args) => $options.goToAnniversary && $options.goToAnniversary(...args)),
    j: common_vendor.o((...args) => $options.goToDiary && $options.goToDiary(...args)),
    k: common_vendor.f($data.moods, (mood, index, i0) => {
      return {
        a: common_vendor.t(mood.emoji),
        b: common_vendor.t(mood.name),
        c: index,
        d: common_vendor.o(($event) => $options.selectMood(mood), index),
        e: $data.selectedMood === mood.name ? 1 : ""
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1cf27b2a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
