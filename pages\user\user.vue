<template>
  <view class="container">
    <!-- 背景装饰 -->
    <view class="bg-decoration">
      <view class="decoration-circle circle-1"></view>
      <view class="decoration-circle circle-2"></view>
      <view class="decoration-circle circle-3"></view>
    </view>

    <!-- 个人信息卡片 -->
    <view class="profile-section">
      <view class="profile-card">
        <view class="avatar-container">
          <image :src="(userInfo && userInfo.avatarUrl) || defaultAvatar" class="profile-avatar" />
          <view class="avatar-border"></view>
          <view class="online-status" v-if="userInfo"></view>
        </view>
        <view class="profile-info">
          <text class="profile-name">{{ (userInfo && userInfo.nickName) || 'Hi, 游客' }}</text>
          <text class="profile-subtitle" v-if="userInfo">{{ userInfo.signature || '还没有个性签名~' }}</text>
          <text class="profile-subtitle" v-else>点击登录，开启甜蜜之旅</text>
        </view>
        <button class="login-btn" @tap="handleLogin" v-if="!userInfo">
          <text class="login-text">微信登录</text>
        </button>
      </view>
    </view>

    <!-- 情侣关系状态 -->
    <view class="relationship-card" v-if="userInfo">
      <view class="relationship-header">
        <text class="relationship-title">情侣关系</text>
        <text class="relationship-status">{{ relationshipStatus }}</text>
      </view>
      <view class="couple-avatars">
        <view class="couple-item">
          <image :src="userInfo.avatarUrl" class="couple-avatar" />
          <text class="couple-name">{{ userInfo.nickName }}</text>
        </view>
        <view class="heart-connector">💕</view>
        <view class="couple-item">
          <image :src="partnerInfo.avatar" class="couple-avatar" />
          <text class="couple-name">{{ partnerInfo.name }}</text>
        </view>
      </view>
      <view class="relationship-days">
        <text class="days-text">在一起 {{ loveDays }} 天</text>
      </view>
    </view>

    <!-- 数据统计 -->
    <view class="stats-section">
      <text class="section-title">我们的数据</text>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-number">{{ stats.photos }}</text>
          <text class="stat-label">照片</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ stats.diaries }}</text>
          <text class="stat-label">日记</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ stats.anniversaries }}</text>
          <text class="stat-label">纪念日</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ stats.messages }}</text>
          <text class="stat-label">消息</text>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-item" @tap="editProfile">
        <view class="menu-icon">👤</view>
        <view class="menu-content">
          <text class="menu-title">编辑资料</text>
          <text class="menu-desc">修改个人信息</text>
        </view>
        <view class="menu-arrow">→</view>
      </view>
      <view class="menu-item" @tap="goToAnniversary">
        <view class="menu-icon">🎉</view>
        <view class="menu-content">
          <text class="menu-title">纪念日管理</text>
          <text class="menu-desc">添加重要日子</text>
        </view>
        <view class="menu-arrow">→</view>
      </view>
      <view class="menu-item" @tap="goToSettings">
        <view class="menu-icon">⚙️</view>
        <view class="menu-content">
          <text class="menu-title">设置</text>
          <text class="menu-desc">隐私与通知</text>
        </view>
        <view class="menu-arrow">→</view>
      </view>
      <view class="menu-item" @tap="goToHelp">
        <view class="menu-icon">❓</view>
        <view class="menu-content">
          <text class="menu-title">帮助与反馈</text>
          <text class="menu-desc">使用指南</text>
        </view>
        <view class="menu-arrow">→</view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-section" v-if="userInfo">
      <button class="logout-btn" @tap="handleLogout">
        <text class="logout-text">退出登录</text>
      </button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userInfo: null,
      defaultAvatar: '/static/default-avatar.png',
      relationshipStatus: '已连接',
      loveDays: 365,
      partnerInfo: {
        avatar: '/static/avatar2.png',
        name: '小帅'
      },
      stats: {
        photos: 128,
        diaries: 45,
        anniversaries: 8,
        messages: 1024
      }
    };
  },
  methods: {
    handleLogin() {
      // 模拟登录成功
      this.userInfo = {
        nickName: '小美',
        avatarUrl: '/static/avatar1.png',
        signature: '每天都要开心呀 💕'
      };
      uni.showToast({ title: '登录成功', icon: 'success' });
    },
    handleLogout() {
      uni.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            this.userInfo = null;
            uni.showToast({ title: '已退出登录', icon: 'success' });
          }
        }
      });
    },
    editProfile() {
      uni.showToast({ title: '编辑个人资料功能待开发', icon: 'none' });
    },
    goToAnniversary() {
      uni.showToast({ title: '纪念日管理功能待开发', icon: 'none' });
    },
    goToSettings() {
      uni.showToast({ title: '设置功能待开发', icon: 'none' });
    },
    goToHelp() {
      uni.showToast({ title: '帮助与反馈功能待开发', icon: 'none' });
    }
  }
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  padding: 30rpx;
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.bg-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: -50rpx;
  animation-delay: 0s;
}

.circle-2 {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  left: -30rpx;
  animation-delay: 2s;
}

.circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 30%;
  left: 50%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20rpx) rotate(180deg);
  }
}

/* 个人信息区域 */
.profile-section {
  position: relative;
  z-index: 2;
  margin-bottom: 30rpx;
}

.profile-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 40rpx;
  text-align: center;
  box-shadow: 0 15rpx 35rpx rgba(255, 105, 180, 0.3);
  backdrop-filter: blur(15rpx);
  animation: slideUp 0.8s ease-out;
}

.avatar-container {
  position: relative;
  display: inline-block;
  margin-bottom: 25rpx;
}

.profile-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  border: 4rpx solid #ff69b4;
  position: relative;
  z-index: 2;
}

.avatar-border {
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  width: 180rpx;
  height: 180rpx;
  border: 2rpx solid rgba(255, 105, 180, 0.3);
  border-radius: 90rpx;
  animation: rotate 4s linear infinite;
}

.online-status {
  position: absolute;
  bottom: 10rpx;
  right: 10rpx;
  width: 24rpx;
  height: 24rpx;
  background: #4caf50;
  border: 3rpx solid white;
  border-radius: 50%;
  z-index: 3;
}

.profile-info {
  margin-bottom: 25rpx;
}

.profile-name {
  font-size: 36rpx;
  color: #ff1493;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}

.profile-subtitle {
  font-size: 24rpx;
  color: #ff69b4;
  opacity: 0.8;
}

.login-btn {
  background: linear-gradient(135deg, #ff69b4, #ff1493);
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  box-shadow: 0 8rpx 20rpx rgba(255, 20, 147, 0.3);
  transition: all 0.3s ease;
}

.login-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 15rpx rgba(255, 20, 147, 0.4);
}

.login-text {
  color: white;
  font-size: 28rpx;
  font-weight: 600;
}

/* 情侣关系卡片 */
.relationship-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 25rpx;
  padding: 35rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(255, 105, 180, 0.25);
  backdrop-filter: blur(10rpx);
  position: relative;
  z-index: 2;
  animation: slideUp 0.8s ease-out 0.2s both;
}

.relationship-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
}

.relationship-title {
  font-size: 30rpx;
  color: #ff1493;
  font-weight: bold;
}

.relationship-status {
  font-size: 22rpx;
  color: #4caf50;
  background: rgba(76, 175, 80, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 15rpx;
}

.couple-avatars {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.couple-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.couple-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  border: 3rpx solid #ff69b4;
  margin-bottom: 10rpx;
}

.couple-name {
  font-size: 22rpx;
  color: #ff1493;
  font-weight: 600;
}

.heart-connector {
  font-size: 40rpx;
  animation: pulse 2s infinite;
}

.relationship-days {
  text-align: center;
}

.days-text {
  font-size: 24rpx;
  color: #ff69b4;
  opacity: 0.8;
}

/* 数据统计 */
.stats-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 25rpx;
  padding: 35rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(255, 105, 180, 0.25);
  backdrop-filter: blur(10rpx);
  position: relative;
  z-index: 2;
  animation: slideUp 0.8s ease-out 0.4s both;
}

.section-title {
  font-size: 30rpx;
  color: #ff1493;
  font-weight: bold;
  margin-bottom: 25rpx;
  display: block;
}

.stats-grid {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  padding: 20rpx 10rpx;
  border-radius: 15rpx;
  transition: all 0.3s ease;
}

.stat-item:active {
  background: rgba(255, 105, 180, 0.1);
  transform: scale(0.95);
}

.stat-number {
  font-size: 36rpx;
  color: #ff1493;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #ff69b4;
  opacity: 0.8;
}

/* 功能菜单 */
.menu-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 25rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(255, 105, 180, 0.25);
  backdrop-filter: blur(10rpx);
  position: relative;
  z-index: 2;
  animation: slideUp 0.8s ease-out 0.6s both;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 25rpx 20rpx;
  border-radius: 15rpx;
  margin-bottom: 10rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.menu-item:last-child {
  margin-bottom: 0;
}

.menu-item:active {
  background: rgba(255, 105, 180, 0.1);
  transform: scale(0.98);
}

.menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.menu-item:active::before {
  left: 100%;
}

.menu-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.menu-content {
  flex: 1;
}

.menu-title {
  font-size: 28rpx;
  color: #ff1493;
  font-weight: 600;
  margin-bottom: 5rpx;
  display: block;
}

.menu-desc {
  font-size: 22rpx;
  color: #ff69b4;
  opacity: 0.7;
}

.menu-arrow {
  font-size: 24rpx;
  color: #ff69b4;
  opacity: 0.6;
}

/* 退出登录 */
.logout-section {
  position: relative;
  z-index: 2;
  animation: slideUp 0.8s ease-out 0.8s both;
}

.logout-btn {
  width: 100%;
  background: rgba(255, 255, 255, 0.9);
  border: 2rpx solid #ff69b4;
  border-radius: 25rpx;
  padding: 25rpx;
  box-shadow: 0 8rpx 20rpx rgba(255, 105, 180, 0.2);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.logout-btn:active {
  transform: scale(0.98);
  background: rgba(255, 105, 180, 0.1);
}

.logout-text {
  color: #ff1493;
  font-size: 28rpx;
  font-weight: 600;
}

/* 动画效果 */
@keyframes slideUp {
  0% {
    transform: translateY(50rpx);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}
</style>
