<template>
  <view class="container">
    <view class="profile-card">
      <image :src="(userInfo && userInfo.avatarUrl) || defaultAvatar" class="profile-avatar" />
      <text class="profile-name">{{ (userInfo && userInfo.nickName) || 'Hi, 游客' }}</text>
      <button @tap="handleLogin" v-if="!userInfo">微信登录</button>
    </view>
    <view class="action-buttons">
      <button @tap="goToAnniversary">查看纪念日</button>
      <button @tap="editProfile">编辑个人资料</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userInfo: null,
      defaultAvatar: '/static/default-avatar.png'
    };
  },
  methods: {
    handleLogin() {
      uni.showToast({ title: '登录功能待开发', icon: 'none' });
    },
    editProfile() {
      uni.showToast({ title: '编辑个人资料功能待开发', icon: 'none' });
    },
    goToAnniversary() {
      uni.showToast({ title: '查看纪念日功能待开发', icon: 'none' });
    }
  }
};
</script>

<style scoped>
body {
  font-family: "Arial", sans-serif;
  background: linear-gradient(135deg, #e1bee7, #f3e5f5);
  overflow: hidden;
}

.container {
  padding: 50rpx;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.profile-card {
  background: #fff;
  width: 95%;
  border-radius: 20rpx;
  text-align: center;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
  padding: 45rpx 25rpx;
  transform: scale(1);
  transition: transform 0.3s ease,
              box-shadow 0.3s ease;
}

.profile-card:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.profile-avatar {
  width: 150rpx;
  height: 150rpx;
  border-radius: 75rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: box-shadow 0.3s ease;
}

.profile-avatar:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.profile-name {
  font-size: 32rpx;
  color: #6a1b9a;
  margin-bottom: 25rpx;
}

.action-buttons {
  margin-top: 40rpx;
  width: 100%;
  display: flex;
  justify-content: space-evenly;
}

.action-buttons button {
  width: 45%;
  background: #ab47bc;
  color: #fff;
  border-radius: 30rpx;
  padding: 20rpx;
  border: none;
  font-size: 28rpx;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  transition: box-shadow 0.3s ease;
}

.action-buttons button:active {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
</style>
