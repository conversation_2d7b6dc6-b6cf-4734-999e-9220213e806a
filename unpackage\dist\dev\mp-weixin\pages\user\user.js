"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      userInfo: null,
      defaultAvatar: "/static/default-avatar.png",
      relationshipStatus: "已连接",
      loveDays: 365,
      partnerInfo: {
        avatar: "/static/avatar2.png",
        name: "小帅"
      },
      stats: {
        photos: 128,
        diaries: 45,
        anniversaries: 8,
        messages: 1024
      }
    };
  },
  methods: {
    handleLogin() {
      this.userInfo = {
        nickName: "小美",
        avatarUrl: "/static/avatar1.png",
        signature: "每天都要开心呀 💕"
      };
      common_vendor.index.showToast({ title: "登录成功", icon: "success" });
    },
    handleLogout() {
      common_vendor.index.showModal({
        title: "确认退出",
        content: "确定要退出登录吗？",
        success: (res) => {
          if (res.confirm) {
            this.userInfo = null;
            common_vendor.index.showToast({ title: "已退出登录", icon: "success" });
          }
        }
      });
    },
    editProfile() {
      common_vendor.index.showToast({ title: "编辑个人资料功能待开发", icon: "none" });
    },
    goToAnniversary() {
      common_vendor.index.showToast({ title: "纪念日管理功能待开发", icon: "none" });
    },
    goToSettings() {
      common_vendor.index.showToast({ title: "设置功能待开发", icon: "none" });
    },
    goToHelp() {
      common_vendor.index.showToast({ title: "帮助与反馈功能待开发", icon: "none" });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.userInfo && $data.userInfo.avatarUrl || $data.defaultAvatar,
    b: $data.userInfo
  }, $data.userInfo ? {} : {}, {
    c: common_vendor.t($data.userInfo && $data.userInfo.nickName || "Hi, 游客"),
    d: $data.userInfo
  }, $data.userInfo ? {
    e: common_vendor.t($data.userInfo.signature || "还没有个性签名~")
  } : {}, {
    f: !$data.userInfo
  }, !$data.userInfo ? {
    g: common_vendor.o((...args) => $options.handleLogin && $options.handleLogin(...args))
  } : {}, {
    h: $data.userInfo
  }, $data.userInfo ? {
    i: common_vendor.t($data.relationshipStatus),
    j: $data.userInfo.avatarUrl,
    k: common_vendor.t($data.userInfo.nickName),
    l: $data.partnerInfo.avatar,
    m: common_vendor.t($data.partnerInfo.name),
    n: common_vendor.t($data.loveDays)
  } : {}, {
    o: common_vendor.t($data.stats.photos),
    p: common_vendor.t($data.stats.diaries),
    q: common_vendor.t($data.stats.anniversaries),
    r: common_vendor.t($data.stats.messages),
    s: common_vendor.o((...args) => $options.editProfile && $options.editProfile(...args)),
    t: common_vendor.o((...args) => $options.goToAnniversary && $options.goToAnniversary(...args)),
    v: common_vendor.o((...args) => $options.goToSettings && $options.goToSettings(...args)),
    w: common_vendor.o((...args) => $options.goToHelp && $options.goToHelp(...args)),
    x: $data.userInfo
  }, $data.userInfo ? {
    y: common_vendor.o((...args) => $options.handleLogout && $options.handleLogout(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-0f7520f0"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user/user.js.map
