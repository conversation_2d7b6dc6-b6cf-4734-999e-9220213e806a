"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      userInfo: null,
      defaultAvatar: "/static/default-avatar.png"
    };
  },
  methods: {
    handleLogin() {
      common_vendor.index.showToast({ title: "登录功能待开发", icon: "none" });
    },
    editProfile() {
      common_vendor.index.showToast({ title: "编辑个人资料功能待开发", icon: "none" });
    },
    goToAnniversary() {
      common_vendor.index.showToast({ title: "查看纪念日功能待开发", icon: "none" });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.userInfo && $data.userInfo.avatarUrl || $data.defaultAvatar,
    b: common_vendor.t($data.userInfo && $data.userInfo.nickName || "Hi, 游客"),
    c: !$data.userInfo
  }, !$data.userInfo ? {
    d: common_vendor.o((...args) => $options.handleLogin && $options.handleLogin(...args))
  } : {}, {
    e: common_vendor.o((...args) => $options.goToAnniversary && $options.goToAnniversary(...args)),
    f: common_vendor.o((...args) => $options.editProfile && $options.editProfile(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-0f7520f0"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user/user.js.map
