// 文档教程: https://uniapp.dcloud.net.cn/uniCloud/schema
{
	"bsonType": "object",
	"required": [],
	"permission": {
		"read": false,
		"create": false,
		"update": false,
		"delete": false
	},
	"properties": {
		"_id": {
			"description": "ID，系统自动生成"
		},
		"username": "13800138000",           // 用户名（手机号）
		  "password": "加密后的密码",           // 登录密码（加密存储）
		  "nickname": "小明",                  // 用户昵称
		  "avatar": "cloud://avatar.jpg",      // 用户头像（云存储文件ID）
		  "gender": "male",                    // 性别：male-男性，female-女性
		  "birthday": "1995-01-01",            // 生日日期
		  "coupleId": "couple123",             // 关联的情侣关系ID
		  "register_date": 1640995200000,      // 注册时间戳
		  "last_login_date": 1640995200000 
	}
}