
.container.data-v-1cf27b2a {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffb3d9, #ffe6f2);
  padding: 30rpx;
  overflow: hidden;
}

/* 情侣信息卡片 */
.couple-card.data-v-1cf27b2a {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 35rpx;
  padding: 45rpx;
  margin-bottom: 35rpx;
  box-shadow: 0 10rpx 35rpx rgba(255, 105, 180, 0.4);
  -webkit-backdrop-filter: blur(12rpx);
          backdrop-filter: blur(12rpx);
  animation: slideDown-1cf27b2a 0.8s ease-out;
}
.love-days.data-v-1cf27b2a {
  text-align: center;
  margin-bottom: 35rpx;
}
.days-number.data-v-1cf27b2a {
  font-size: 80rpx;
  font-weight: bold;
  color: #ff1493;
  display: block;
}
.days-text.data-v-1cf27b2a {
  font-size: 24rpx;
  color: #ff69b4;
}
.couple-info.data-v-1cf27b2a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.user-avatar.data-v-1cf27b2a {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.avatar.data-v-1cf27b2a {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid #ff69b4;
  margin-bottom: 10rpx;
}
.username.data-v-1cf27b2a {
  font-size: 24rpx;
  color: #ff1493;
  font-weight: 600;
}
.heart-icon.data-v-1cf27b2a {
  font-size: 60rpx;
  animation: heartBeat-1cf27b2a 2s infinite;
}
.love-date.data-v-1cf27b2a {
  text-align: center;
  font-size: 24rpx;
  color: #ff69b4;
}

/* 快捷功能区 */
.quick-actions.data-v-1cf27b2a {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 30rpx;
}
.action-item.data-v-1cf27b2a {
  width: 48%;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 180, 0.2);
  transition: transform 0.3s ease;
}
.action-item.data-v-1cf27b2a:active {
  transform: scale(0.95);
}
.action-icon.data-v-1cf27b2a {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}
.action-text.data-v-1cf27b2a {
  font-size: 26rpx;
  color: #ff1493;
  font-weight: 600;
}

/* 心情区域 */
.mood-section.data-v-1cf27b2a {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  -webkit-backdrop-filter: blur(5rpx);
          backdrop-filter: blur(5rpx);
}
.section-title.data-v-1cf27b2a {
  font-size: 32rpx;
  color: #ff1493;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: block;
}
.mood-list.data-v-1cf27b2a {
  display: flex;
  justify-content: space-between;
}
.mood-item.data-v-1cf27b2a {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx;
  border-radius: 15rpx;
  transition: background-color 0.3s ease;
}
.mood-item.data-v-1cf27b2a:active {
  background-color: rgba(255, 105, 180, 0.1);
}
.mood-emoji.data-v-1cf27b2a {
  font-size: 36rpx;
  margin-bottom: 8rpx;
}
.mood-name.data-v-1cf27b2a {
  font-size: 20rpx;
  color: #ff69b4;
}

/* 纪念日区域 */
.anniversary-section.data-v-1cf27b2a {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  padding: 30rpx;
  -webkit-backdrop-filter: blur(5rpx);
          backdrop-filter: blur(5rpx);
}
.anniversary-card.data-v-1cf27b2a {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #ff69b4, #ff1493);
  border-radius: 15rpx;
  padding: 25rpx;
  color: white;
}
.anniversary-icon.data-v-1cf27b2a {
  font-size: 48rpx;
  margin-right: 20rpx;
}
.anniversary-info.data-v-1cf27b2a {
  flex: 1;
}
.anniversary-name.data-v-1cf27b2a {
  font-size: 28rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 5rpx;
}
.anniversary-date.data-v-1cf27b2a {
  font-size: 22rpx;
  opacity: 0.9;
}

/* 动画效果 */
@keyframes slideDown-1cf27b2a {
0% {
    transform: translateY(-30rpx);
    opacity: 0;
}
100% {
    transform: translateY(0);
    opacity: 1;
}
}
@keyframes heartBeat-1cf27b2a {
0%, 100% {
    transform: scale(1);
}
50% {
    transform: scale(1.1);
}
}
