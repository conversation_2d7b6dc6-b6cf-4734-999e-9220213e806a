
.container.data-v-1cf27b2a {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  padding: 30rpx;
  overflow: hidden;
  position: relative;
}

/* 背景粒子动画 */
.particles.data-v-1cf27b2a {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}
.particle.data-v-1cf27b2a {
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: float-1cf27b2a 5s infinite linear;
}
@keyframes float-1cf27b2a {
0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
}
10% {
    opacity: 1;
}
90% {
    opacity: 1;
}
100% {
    transform: translateY(-100rpx) rotate(360deg);
    opacity: 0;
}
}

/* 每日情话 */
.daily-quote.data-v-1cf27b2a {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 25rpx;
  padding: 25rpx 30rpx;
  margin-bottom: 30rpx;
  position: relative;
  box-shadow: 0 8rpx 25rpx rgba(255, 105, 180, 0.3);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  animation: slideDown-1cf27b2a 0.6s ease-out;
  z-index: 2;
}
.quote-text.data-v-1cf27b2a {
  font-size: 26rpx;
  color: #ff1493;
  line-height: 1.5;
  display: block;
  margin-bottom: 10rpx;
}
.quote-icon.data-v-1cf27b2a {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  font-size: 24rpx;
  animation: pulse-1cf27b2a 2s infinite;
}

/* 情侣信息卡片 */
.couple-card.data-v-1cf27b2a {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 35rpx;
  padding: 45rpx;
  margin-bottom: 35rpx;
  box-shadow: 0 15rpx 40rpx rgba(255, 105, 180, 0.4);
  -webkit-backdrop-filter: blur(15rpx);
          backdrop-filter: blur(15rpx);
  animation: slideDown-1cf27b2a 0.8s ease-out;
  position: relative;
  z-index: 2;
}
.love-days.data-v-1cf27b2a {
  text-align: center;
  margin-bottom: 40rpx;
}
.days-number.data-v-1cf27b2a {
  font-size: 88rpx;
  font-weight: bold;
  color: #ff1493;
  display: block;
  text-shadow: 0 2rpx 8rpx rgba(255, 20, 147, 0.3);
}
.days-text.data-v-1cf27b2a {
  font-size: 28rpx;
  color: #ff69b4;
  margin-left: 10rpx;
}
.love-subtitle.data-v-1cf27b2a {
  font-size: 22rpx;
  color: #ff69b4;
  margin-top: 10rpx;
  display: block;
  opacity: 0.8;
}
.couple-info.data-v-1cf27b2a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 25rpx;
}
.user-avatar.data-v-1cf27b2a {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
.avatar-container.data-v-1cf27b2a {
  position: relative;
  margin-bottom: 15rpx;
}
.avatar.data-v-1cf27b2a {
  width: 130rpx;
  height: 130rpx;
  border-radius: 65rpx;
  border: 3rpx solid #ff69b4;
}
.avatar-ring.data-v-1cf27b2a {
  position: absolute;
  top: -8rpx;
  left: -8rpx;
  width: 146rpx;
  height: 146rpx;
  border: 2rpx solid rgba(255, 105, 180, 0.3);
  border-radius: 73rpx;
  animation: rotate-1cf27b2a 3s linear infinite;
}
.username.data-v-1cf27b2a {
  font-size: 26rpx;
  color: #ff1493;
  font-weight: 600;
  margin-bottom: 5rpx;
}
.user-status.data-v-1cf27b2a {
  font-size: 20rpx;
  color: #ff69b4;
  opacity: 0.7;
}
.heart-container.data-v-1cf27b2a {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.heart-icon.data-v-1cf27b2a {
  font-size: 65rpx;
  animation: heartBeat-1cf27b2a 2s infinite;
  z-index: 2;
}
.love-line.data-v-1cf27b2a {
  position: absolute;
  top: 50%;
  left: -80rpx;
  right: -80rpx;
  height: 2rpx;
  background: linear-gradient(90deg, transparent, #ff69b4, transparent);
  z-index: 1;
}
.love-date.data-v-1cf27b2a {
  text-align: center;
  font-size: 24rpx;
  color: #ff69b4;
  opacity: 0.8;
}

/* 快捷功能区 */
.quick-actions.data-v-1cf27b2a {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 35rpx;
  z-index: 2;
  position: relative;
}
.action-item.data-v-1cf27b2a {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 25rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 8rpx 25rpx rgba(255, 105, 180, 0.25);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.action-item.data-v-1cf27b2a:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 15rpx rgba(255, 105, 180, 0.4);
}
.action-item.data-v-1cf27b2a::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}
.action-item.data-v-1cf27b2a:active::before {
  left: 100%;
}
.action-icon-container.data-v-1cf27b2a {
  position: relative;
  margin-bottom: 12rpx;
}
.action-icon.data-v-1cf27b2a {
  font-size: 48rpx;
  position: relative;
  z-index: 2;
}
.icon-bg.data-v-1cf27b2a {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 70rpx;
  height: 70rpx;
  background: linear-gradient(135deg, rgba(255, 105, 180, 0.1), rgba(255, 20, 147, 0.1));
  border-radius: 50%;
  z-index: 1;
}
.action-text.data-v-1cf27b2a {
  font-size: 26rpx;
  color: #ff1493;
  font-weight: 600;
  margin-bottom: 4rpx;
  display: block;
}
.action-desc.data-v-1cf27b2a {
  font-size: 18rpx;
  color: #ff69b4;
  opacity: 0.7;
  line-height: 1.2;
}

/* 心情区域 */
.mood-section.data-v-1cf27b2a {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 25rpx;
  padding: 35rpx;
  margin-bottom: 35rpx;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 25rpx rgba(255, 105, 180, 0.2);
  position: relative;
  z-index: 2;
}
.section-header.data-v-1cf27b2a {
  margin-bottom: 25rpx;
}
.section-title.data-v-1cf27b2a {
  font-size: 34rpx;
  color: #ff1493;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
}
.section-subtitle.data-v-1cf27b2a {
  font-size: 22rpx;
  color: #ff69b4;
  opacity: 0.7;
}
.mood-list.data-v-1cf27b2a {
  display: flex;
  justify-content: space-between;
}
.mood-item.data-v-1cf27b2a {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 15rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.mood-item.data-v-1cf27b2a:active {
  transform: scale(0.95);
}
.mood-item.selected.data-v-1cf27b2a {
  background: linear-gradient(135deg, #ff69b4, #ff1493);
  color: white;
}
.mood-item.selected .mood-name.data-v-1cf27b2a {
  color: white;
}
.mood-emoji-container.data-v-1cf27b2a {
  margin-bottom: 10rpx;
  position: relative;
}
.mood-emoji.data-v-1cf27b2a {
  font-size: 40rpx;
  transition: transform 0.3s ease;
}
.mood-item:active .mood-emoji.data-v-1cf27b2a {
  transform: scale(1.2);
}
.mood-name.data-v-1cf27b2a {
  font-size: 22rpx;
  color: #ff69b4;
  font-weight: 500;
  transition: color 0.3s ease;
}

/* 纪念日区域 */
.anniversary-section.data-v-1cf27b2a {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 25rpx;
  padding: 35rpx;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 25rpx rgba(255, 105, 180, 0.2);
  position: relative;
  z-index: 2;
}
.anniversary-card.data-v-1cf27b2a {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #ff69b4, #ff1493);
  border-radius: 20rpx;
  padding: 30rpx;
  color: white;
  box-shadow: 0 8rpx 20rpx rgba(255, 20, 147, 0.3);
  transition: transform 0.3s ease;
  position: relative;
  overflow: hidden;
}
.anniversary-card.data-v-1cf27b2a:active {
  transform: scale(0.98);
}
.anniversary-card.data-v-1cf27b2a::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}
.anniversary-card.data-v-1cf27b2a:active::before {
  left: 100%;
}
.anniversary-icon.data-v-1cf27b2a {
  font-size: 52rpx;
  margin-right: 25rpx;
  animation: bounce-1cf27b2a 2s infinite;
}
.anniversary-info.data-v-1cf27b2a {
  flex: 1;
}
.anniversary-name.data-v-1cf27b2a {
  font-size: 30rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}
.anniversary-date.data-v-1cf27b2a {
  font-size: 24rpx;
  opacity: 0.9;
  margin-bottom: 10rpx;
}
.anniversary-progress.data-v-1cf27b2a {
  width: 100%;
  height: 6rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3rpx;
  overflow: hidden;
}
.progress-bar.data-v-1cf27b2a {
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 3rpx;
  transition: width 0.3s ease;
}
.anniversary-arrow.data-v-1cf27b2a {
  font-size: 24rpx;
  margin-left: 15rpx;
  opacity: 0.8;
}

/* 动画效果 */
@keyframes slideDown-1cf27b2a {
0% {
    transform: translateY(-30rpx);
    opacity: 0;
}
100% {
    transform: translateY(0);
    opacity: 1;
}
}
@keyframes heartBeat-1cf27b2a {
0%, 100% {
    transform: scale(1);
}
50% {
    transform: scale(1.1);
}
}
@keyframes pulse-1cf27b2a {
0%, 100% {
    transform: scale(1);
    opacity: 1;
}
50% {
    transform: scale(1.05);
    opacity: 0.8;
}
}
@keyframes rotate-1cf27b2a {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes bounce-1cf27b2a {
0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
}
40% {
    transform: translateY(-10rpx);
}
60% {
    transform: translateY(-5rpx);
}
}
