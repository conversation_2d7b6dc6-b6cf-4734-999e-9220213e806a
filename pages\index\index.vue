<template>
  <view class="container">
    <!-- 背景粒子动画 -->
    <view class="particles">
      <view class="particle" v-for="n in 20" :key="n" :style="getParticleStyle(n)"></view>
    </view>

    <!-- 每日情话 -->
    <view class="daily-quote">
      <text class="quote-text">{{ dailyQuote }}</text>
      <view class="quote-icon">💕</view>
    </view>

    <!-- 情侣信息卡片 -->
    <view class="couple-card">
      <view class="love-days">
        <text class="days-number">{{ loveDays }}</text>
        <text class="days-text">天</text>
        <text class="love-subtitle">我们在一起的美好时光</text>
      </view>
      <view class="couple-info">
        <view class="user-avatar">
          <view class="avatar-container">
            <image :src="userAvatar" class="avatar"></image>
            <view class="avatar-ring"></view>
          </view>
          <text class="username">小美</text>
          <text class="user-status">在线</text>
        </view>
        <view class="heart-container">
          <view class="heart-icon">💞</view>
          <view class="love-line"></view>
        </view>
        <view class="user-avatar">
          <view class="avatar-container">
            <image :src="partnerAvatar" class="avatar"></image>
            <view class="avatar-ring"></view>
          </view>
          <text class="username">小帅</text>
          <text class="user-status">2分钟前</text>
        </view>
      </view>
      <text class="love-date">恋爱开始于 {{ loveStartDate }}</text>
    </view>

    <!-- 快捷功能区 -->
    <view class="quick-actions">
      <view class="action-item" @tap="goToAlbum">
        <view class="action-icon-container">
          <text class="action-icon">📷</text>
          <view class="icon-bg"></view>
        </view>
        <text class="action-text">相册</text>
        <text class="action-desc">记录美好瞬间</text>
      </view>
      <view class="action-item" @tap="goToChat">
        <view class="action-icon-container">
          <text class="action-icon">💬</text>
          <view class="icon-bg"></view>
        </view>
        <text class="action-text">聊天</text>
        <text class="action-desc">甜蜜对话</text>
      </view>
      <view class="action-item" @tap="goToAnniversary">
        <view class="action-icon-container">
          <text class="action-icon">🎉</text>
          <view class="icon-bg"></view>
        </view>
        <text class="action-text">纪念日</text>
        <text class="action-desc">重要时刻</text>
      </view>
      <view class="action-item" @tap="goToDiary">
        <view class="action-icon-container">
          <text class="action-icon">📝</text>
          <view class="icon-bg"></view>
        </view>
        <text class="action-text">日记</text>
        <text class="action-desc">心情记录</text>
      </view>
    </view>

    <!-- 今日心情 -->
    <view class="mood-section">
      <view class="section-header">
        <text class="section-title">今日心情</text>
        <text class="section-subtitle">分享你的感受</text>
      </view>
      <view class="mood-list">
        <view class="mood-item" v-for="(mood, index) in moods" :key="index" @tap="selectMood(mood)" :class="{ 'selected': selectedMood === mood.name }">
          <view class="mood-emoji-container">
            <text class="mood-emoji">{{ mood.emoji }}</text>
          </view>
          <text class="mood-name">{{ mood.name }}</text>
        </view>
      </view>
    </view>

    <!-- 最近纪念日 -->
    <view class="anniversary-section">
      <view class="section-header">
        <text class="section-title">即将到来</text>
        <text class="section-subtitle">不要错过重要日子</text>
      </view>
      <view class="anniversary-card">
        <view class="anniversary-icon">🎂</view>
        <view class="anniversary-info">
          <text class="anniversary-name">我们的生日</text>
          <text class="anniversary-date">还有 15 天</text>
          <view class="anniversary-progress">
            <view class="progress-bar" style="width: 70%"></view>
          </view>
        </view>
        <view class="anniversary-arrow">→</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loveDays: 365,
      loveStartDate: '2023-08-04',
      userAvatar: '/static/avatar1.png',
      partnerAvatar: '/static/avatar2.png',
      selectedMood: '',
      dailyQuote: '爱情不是寻找一个完美的人，而是学会用完美的眼光欣赏不完美的人 💕',
      moods: [
        { emoji: '😊', name: '开心' },
        { emoji: '😍', name: '甜蜜' },
        { emoji: '🥰', name: '幸福' },
        { emoji: '😌', name: '平静' },
        { emoji: '😔', name: '想念' }
      ]
    };
  },
  methods: {
    goToAlbum() {
      uni.showToast({ title: '相册功能待开发', icon: 'none' });
    },
    goToChat() {
      uni.showToast({ title: '聊天功能待开发', icon: 'none' });
    },
    goToAnniversary() {
      uni.showToast({ title: '纪念日功能待开发', icon: 'none' });
    },
    goToDiary() {
      uni.showToast({ title: '日记功能待开发', icon: 'none' });
    },
    selectMood(mood) {
      this.selectedMood = mood.name;
      uni.showToast({ title: `选择了${mood.name}心情`, icon: 'none' });
    },
    getParticleStyle(index) {
      const delay = Math.random() * 3;
      const duration = 3 + Math.random() * 2;
      const left = Math.random() * 100;
      return {
        left: left + '%',
        animationDelay: delay + 's',
        animationDuration: duration + 's'
      };
    }
  }
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  padding: 30rpx;
  overflow: hidden;
  position: relative;
}

/* 背景粒子动画 */
.particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.particle {
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: float 5s infinite linear;
}

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100rpx) rotate(360deg);
    opacity: 0;
  }
}

/* 每日情话 */
.daily-quote {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 25rpx;
  padding: 25rpx 30rpx;
  margin-bottom: 30rpx;
  position: relative;
  box-shadow: 0 8rpx 25rpx rgba(255, 105, 180, 0.3);
  backdrop-filter: blur(10rpx);
  animation: slideDown 0.6s ease-out;
  z-index: 2;
}

.quote-text {
  font-size: 26rpx;
  color: #ff1493;
  line-height: 1.5;
  display: block;
  margin-bottom: 10rpx;
}

.quote-icon {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  font-size: 24rpx;
  animation: pulse 2s infinite;
}

/* 情侣信息卡片 */
.couple-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 35rpx;
  padding: 45rpx;
  margin-bottom: 35rpx;
  box-shadow: 0 15rpx 40rpx rgba(255, 105, 180, 0.4);
  backdrop-filter: blur(15rpx);
  animation: slideDown 0.8s ease-out;
  position: relative;
  z-index: 2;
}

.love-days {
  text-align: center;
  margin-bottom: 40rpx;
}

.days-number {
  font-size: 88rpx;
  font-weight: bold;
  color: #ff1493;
  display: block;
  text-shadow: 0 2rpx 8rpx rgba(255, 20, 147, 0.3);
}

.days-text {
  font-size: 28rpx;
  color: #ff69b4;
  margin-left: 10rpx;
}

.love-subtitle {
  font-size: 22rpx;
  color: #ff69b4;
  margin-top: 10rpx;
  display: block;
  opacity: 0.8;
}

.couple-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 25rpx;
}

.user-avatar {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.avatar-container {
  position: relative;
  margin-bottom: 15rpx;
}

.avatar {
  width: 130rpx;
  height: 130rpx;
  border-radius: 65rpx;
  border: 3rpx solid #ff69b4;
}

.avatar-ring {
  position: absolute;
  top: -8rpx;
  left: -8rpx;
  width: 146rpx;
  height: 146rpx;
  border: 2rpx solid rgba(255, 105, 180, 0.3);
  border-radius: 73rpx;
  animation: rotate 3s linear infinite;
}

.username {
  font-size: 26rpx;
  color: #ff1493;
  font-weight: 600;
  margin-bottom: 5rpx;
}

.user-status {
  font-size: 20rpx;
  color: #ff69b4;
  opacity: 0.7;
}

.heart-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.heart-icon {
  font-size: 65rpx;
  animation: heartBeat 2s infinite;
  z-index: 2;
}

.love-line {
  position: absolute;
  top: 50%;
  left: -80rpx;
  right: -80rpx;
  height: 2rpx;
  background: linear-gradient(90deg, transparent, #ff69b4, transparent);
  z-index: 1;
}

.love-date {
  text-align: center;
  font-size: 24rpx;
  color: #ff69b4;
  opacity: 0.8;
}

/* 快捷功能区 */
.quick-actions {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 35rpx;
  z-index: 2;
  position: relative;
}

.action-item {
  width: 48%;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 25rpx;
  padding: 35rpx 20rpx;
  text-align: center;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 25rpx rgba(255, 105, 180, 0.25);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-item:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 15rpx rgba(255, 105, 180, 0.4);
}

.action-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.action-item:active::before {
  left: 100%;
}

.action-icon-container {
  position: relative;
  margin-bottom: 15rpx;
}

.action-icon {
  font-size: 52rpx;
  position: relative;
  z-index: 2;
}

.icon-bg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, rgba(255, 105, 180, 0.1), rgba(255, 20, 147, 0.1));
  border-radius: 50%;
  z-index: 1;
}

.action-text {
  font-size: 28rpx;
  color: #ff1493;
  font-weight: 600;
  margin-bottom: 5rpx;
  display: block;
}

.action-desc {
  font-size: 20rpx;
  color: #ff69b4;
  opacity: 0.7;
}

/* 心情区域 */
.mood-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 25rpx;
  padding: 35rpx;
  margin-bottom: 35rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 25rpx rgba(255, 105, 180, 0.2);
  position: relative;
  z-index: 2;
}

.section-header {
  margin-bottom: 25rpx;
}

.section-title {
  font-size: 34rpx;
  color: #ff1493;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
}

.section-subtitle {
  font-size: 22rpx;
  color: #ff69b4;
  opacity: 0.7;
}

.mood-list {
  display: flex;
  justify-content: space-between;
}

.mood-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 15rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.mood-item:active {
  transform: scale(0.95);
}

.mood-item.selected {
  background: linear-gradient(135deg, #ff69b4, #ff1493);
  color: white;
}

.mood-item.selected .mood-name {
  color: white;
}

.mood-emoji-container {
  margin-bottom: 10rpx;
  position: relative;
}

.mood-emoji {
  font-size: 40rpx;
  transition: transform 0.3s ease;
}

.mood-item:active .mood-emoji {
  transform: scale(1.2);
}

.mood-name {
  font-size: 22rpx;
  color: #ff69b4;
  font-weight: 500;
  transition: color 0.3s ease;
}

/* 纪念日区域 */
.anniversary-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 25rpx;
  padding: 35rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 25rpx rgba(255, 105, 180, 0.2);
  position: relative;
  z-index: 2;
}

.anniversary-card {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #ff69b4, #ff1493);
  border-radius: 20rpx;
  padding: 30rpx;
  color: white;
  box-shadow: 0 8rpx 20rpx rgba(255, 20, 147, 0.3);
  transition: transform 0.3s ease;
  position: relative;
  overflow: hidden;
}

.anniversary-card:active {
  transform: scale(0.98);
}

.anniversary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.anniversary-card:active::before {
  left: 100%;
}

.anniversary-icon {
  font-size: 52rpx;
  margin-right: 25rpx;
  animation: bounce 2s infinite;
}

.anniversary-info {
  flex: 1;
}

.anniversary-name {
  font-size: 30rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.anniversary-date {
  font-size: 24rpx;
  opacity: 0.9;
  margin-bottom: 10rpx;
}

.anniversary-progress {
  width: 100%;
  height: 6rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

.anniversary-arrow {
  font-size: 24rpx;
  margin-left: 15rpx;
  opacity: 0.8;
}

/* 动画效果 */
@keyframes slideDown {
  0% {
    transform: translateY(-30rpx);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes heartBeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}
</style>
