<template>
  <view class="container">
    <!-- 情侣信息卡片 -->
    <view class="couple-card">
      <view class="love-days">
        <text class="days-number">{{ loveDays }}</text>
        <text class="days-text">天</text>
      </view>
      <view class="couple-info">
        <view class="user-avatar">
          <image :src="userAvatar" class="avatar"></image>
          <text class="username">小美</text>
        </view>
        <view class="heart-icon">💞</view>
        <view class="user-avatar">
          <image :src="partnerAvatar" class="avatar"></image>
          <text class="username">小帅</text>
        </view>
      </view>
      <text class="love-date">恋爱开始于 {{ loveStartDate }}</text>
    </view>

    <!-- 快捷功能区 -->
    <view class="quick-actions">
      <view class="action-item" @tap="goToAlbum">
        <view class="action-icon">📷</view>
        <text class="action-text">相册</text>
      </view>
      <view class="action-item" @tap="goToChat">
        <view class="action-icon">💬</view>
        <text class="action-text">聊天</text>
      </view>
      <view class="action-item" @tap="goToAnniversary">
        <view class="action-icon">🎉</view>
        <text class="action-text">纪念日</text>
      </view>
      <view class="action-item" @tap="goToDiary">
        <view class="action-icon">📝</view>
        <text class="action-text">日记</text>
      </view>
    </view>

    <!-- 今日心情 -->
    <view class="mood-section">
      <text class="section-title">今日心情</text>
      <view class="mood-list">
        <view class="mood-item" v-for="(mood, index) in moods" :key="index" @tap="selectMood(mood)">
          <text class="mood-emoji">{{ mood.emoji }}</text>
          <text class="mood-name">{{ mood.name }}</text>
        </view>
      </view>
    </view>

    <!-- 最近纪念日 -->
    <view class="anniversary-section">
      <text class="section-title">即将到来</text>
      <view class="anniversary-card">
        <view class="anniversary-icon">🎂</view>
        <view class="anniversary-info">
          <text class="anniversary-name">我们的生日</text>
          <text class="anniversary-date">还有 15 天</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loveDays: 365,
      loveStartDate: '2023-08-04',
      userAvatar: '/static/avatar1.png',
      partnerAvatar: '/static/avatar2.png',
      moods: [
        { emoji: '😊', name: '开心' },
        { emoji: '😍', name: '甜蜜' },
        { emoji: '🥰', name: '幸福' },
        { emoji: '😌', name: '平静' },
        { emoji: '😔', name: '想念' }
      ]
    };
  },
  methods: {
    goToAlbum() {
      uni.showToast({ title: '相册功能待开发', icon: 'none' });
    },
    goToChat() {
      uni.showToast({ title: '聊天功能待开发', icon: 'none' });
    },
    goToAnniversary() {
      uni.showToast({ title: '纪念日功能待开发', icon: 'none' });
    },
    goToDiary() {
      uni.showToast({ title: '日记功能待开发', icon: 'none' });
    },
    selectMood(mood) {
      uni.showToast({ title: `选择了${mood.name}心情`, icon: 'none' });
    }
  }
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffb3d9, #ffe6f2);
  padding: 30rpx;
  overflow: hidden;
}

/* 情侣信息卡片 */
.couple-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 35rpx;
  padding: 45rpx;
  margin-bottom: 35rpx;
  box-shadow: 0 10rpx 35rpx rgba(255, 105, 180, 0.4);
  backdrop-filter: blur(12rpx);
  animation: slideDown 0.8s ease-out;
}

.love-days {
  text-align: center;
  margin-bottom: 35rpx;
}

.days-number {
  font-size: 80rpx;
  font-weight: bold;
  color: #ff1493;
  display: block;
}

.days-text {
  font-size: 24rpx;
  color: #ff69b4;
}

.couple-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.user-avatar {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid #ff69b4;
  margin-bottom: 10rpx;
}

.username {
  font-size: 24rpx;
  color: #ff1493;
  font-weight: 600;
}

.heart-icon {
  font-size: 60rpx;
  animation: heartBeat 2s infinite;
}

.love-date {
  text-align: center;
  font-size: 24rpx;
  color: #ff69b4;
}

/* 快捷功能区 */
.quick-actions {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.action-item {
  width: 48%;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 180, 0.2);
  transition: transform 0.3s ease;
}

.action-item:active {
  transform: scale(0.95);
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.action-text {
  font-size: 26rpx;
  color: #ff1493;
  font-weight: 600;
}

/* 心情区域 */
.mood-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(5rpx);
}

.section-title {
  font-size: 32rpx;
  color: #ff1493;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: block;
}

.mood-list {
  display: flex;
  justify-content: space-between;
}

.mood-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx;
  border-radius: 15rpx;
  transition: background-color 0.3s ease;
}

.mood-item:active {
  background-color: rgba(255, 105, 180, 0.1);
}

.mood-emoji {
  font-size: 36rpx;
  margin-bottom: 8rpx;
}

.mood-name {
  font-size: 20rpx;
  color: #ff69b4;
}

/* 纪念日区域 */
.anniversary-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  padding: 30rpx;
  backdrop-filter: blur(5rpx);
}

.anniversary-card {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #ff69b4, #ff1493);
  border-radius: 15rpx;
  padding: 25rpx;
  color: white;
}

.anniversary-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.anniversary-info {
  flex: 1;
}

.anniversary-name {
  font-size: 28rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 5rpx;
}

.anniversary-date {
  font-size: 22rpx;
  opacity: 0.9;
}

/* 动画效果 */
@keyframes slideDown {
  0% {
    transform: translateY(-30rpx);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes heartBeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}
</style>
