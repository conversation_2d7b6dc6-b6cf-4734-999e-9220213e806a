
.container.data-v-018cdf56 {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  padding: 30rpx;
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.bg-decoration.data-v-018cdf56 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}
.decoration-circle.data-v-018cdf56 {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float-018cdf56 6s ease-in-out infinite;
}
.circle-1.data-v-018cdf56 {
  width: 180rpx;
  height: 180rpx;
  top: 15%;
  right: -40rpx;
  animation-delay: 0s;
}
.circle-2.data-v-018cdf56 {
  width: 120rpx;
  height: 120rpx;
  top: 70%;
  left: -20rpx;
  animation-delay: 2s;
}
.circle-3.data-v-018cdf56 {
  width: 80rpx;
  height: 80rpx;
  top: 40%;
  left: 60%;
  animation-delay: 4s;
}
@keyframes float-018cdf56 {
0%, 100% {
    transform: translateY(0px) rotate(0deg);
}
50% {
    transform: translateY(-15rpx) rotate(180deg);
}
}

/* 设置区域 */
.setting-section.data-v-018cdf56 {
  position: relative;
  z-index: 2;
  margin-bottom: 30rpx;
  animation: slideUp-018cdf56 0.8s ease-out;
}
.setting-section.data-v-018cdf56:nth-child(2) { animation-delay: 0.1s;
}
.setting-section.data-v-018cdf56:nth-child(3) { animation-delay: 0.2s;
}
.setting-section.data-v-018cdf56:nth-child(4) { animation-delay: 0.3s;
}
.setting-section.data-v-018cdf56:nth-child(5) { animation-delay: 0.4s;
}
.setting-section.data-v-018cdf56:nth-child(6) { animation-delay: 0.5s;
}
.setting-section.data-v-018cdf56:nth-child(7) { animation-delay: 0.6s;
}
.section-header.data-v-018cdf56 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15rpx;
  padding: 0 10rpx;
}
.section-title.data-v-018cdf56 {
  font-size: 32rpx;
  color: #ff1493;
  font-weight: bold;
}
.section-icon.data-v-018cdf56 {
  font-size: 28rpx;
}
.setting-card.data-v-018cdf56 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 25rpx;
  -webkit-backdrop-filter: blur(15rpx);
          backdrop-filter: blur(15rpx);
  box-shadow: 0 10rpx 30rpx rgba(255, 105, 180, 0.25);
  overflow: hidden;
}

/* 设置项 */
.setting-item.data-v-018cdf56 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid rgba(255, 105, 180, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.setting-item.data-v-018cdf56:last-child {
  border-bottom: none;
}
.setting-item.data-v-018cdf56:active {
  background: rgba(255, 105, 180, 0.05);
  transform: scale(0.98);
}
.setting-item.data-v-018cdf56::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}
.setting-item.data-v-018cdf56:active::before {
  left: 100%;
}
.setting-left.data-v-018cdf56 {
  display: flex;
  align-items: center;
  flex: 1;
}
.setting-icon.data-v-018cdf56 {
  font-size: 32rpx;
  margin-right: 20rpx;
  width: 40rpx;
  text-align: center;
}
.setting-label.data-v-018cdf56 {
  font-size: 28rpx;
  color: #ff1493;
  font-weight: 500;
}
.setting-right.data-v-018cdf56 {
  display: flex;
  align-items: center;
}
.setting-value.data-v-018cdf56 {
  font-size: 24rpx;
  color: #ff69b4;
  opacity: 0.8;
  margin-right: 10rpx;
}
.setting-arrow.data-v-018cdf56 {
  font-size: 20rpx;
  color: #ff69b4;
  opacity: 0.6;
}

/* 退出登录 */
.logout-section.data-v-018cdf56 {
  position: relative;
  z-index: 2;
  animation: slideUp-018cdf56 0.8s ease-out 0.7s both;
}
.logout-btn.data-v-018cdf56 {
  width: 100%;
  background: rgba(255, 255, 255, 0.9);
  border: 2rpx solid #ff69b4;
  border-radius: 25rpx;
  padding: 25rpx;
  box-shadow: 0 8rpx 20rpx rgba(255, 105, 180, 0.2);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}
.logout-btn.data-v-018cdf56:active {
  transform: scale(0.98);
  background: rgba(255, 105, 180, 0.1);
  box-shadow: 0 4rpx 15rpx rgba(255, 105, 180, 0.3);
}
.logout-text.data-v-018cdf56 {
  color: #ff1493;
  font-size: 28rpx;
  font-weight: 600;
}

/* 动画效果 */
@keyframes slideUp-018cdf56 {
0% {
    transform: translateY(50rpx);
    opacity: 0;
}
100% {
    transform: translateY(0);
    opacity: 1;
}
}
