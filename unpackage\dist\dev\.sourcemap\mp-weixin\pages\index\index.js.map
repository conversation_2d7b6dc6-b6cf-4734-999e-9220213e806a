{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 背景粒子动画 -->\n    <view class=\"particles\">\n      <view class=\"particle\" v-for=\"n in 20\" :key=\"n\" :style=\"getParticleStyle(n)\"></view>\n    </view>\n\n    <!-- 每日情话 -->\n    <view class=\"daily-quote\">\n      <text class=\"quote-text\">{{ dailyQuote }}</text>\n      <view class=\"quote-icon\">💕</view>\n    </view>\n\n    <!-- 情侣信息卡片 -->\n    <view class=\"couple-card\">\n      <view class=\"love-days\">\n        <text class=\"days-number\">{{ loveDays }}</text>\n        <text class=\"days-text\">天</text>\n        <text class=\"love-subtitle\">我们在一起的美好时光</text>\n      </view>\n      <view class=\"couple-info\">\n        <view class=\"user-avatar\">\n          <view class=\"avatar-container\">\n            <image :src=\"userAvatar\" class=\"avatar\"></image>\n            <view class=\"avatar-ring\"></view>\n          </view>\n          <text class=\"username\">小美</text>\n          <text class=\"user-status\">在线</text>\n        </view>\n        <view class=\"heart-container\">\n          <view class=\"heart-icon\">💞</view>\n          <view class=\"love-line\"></view>\n        </view>\n        <view class=\"user-avatar\">\n          <view class=\"avatar-container\">\n            <image :src=\"partnerAvatar\" class=\"avatar\"></image>\n            <view class=\"avatar-ring\"></view>\n          </view>\n          <text class=\"username\">小帅</text>\n          <text class=\"user-status\">2分钟前</text>\n        </view>\n      </view>\n      <text class=\"love-date\">恋爱开始于 {{ loveStartDate }}</text>\n    </view>\n\n    <!-- 快捷功能区 -->\n    <view class=\"quick-actions\">\n      <view class=\"action-item\" @tap=\"goToAlbum\">\n        <view class=\"action-icon-container\">\n          <text class=\"action-icon\">📷</text>\n          <view class=\"icon-bg\"></view>\n        </view>\n        <text class=\"action-text\">相册</text>\n        <text class=\"action-desc\">记录美好瞬间</text>\n      </view>\n      <view class=\"action-item\" @tap=\"goToChat\">\n        <view class=\"action-icon-container\">\n          <text class=\"action-icon\">💬</text>\n          <view class=\"icon-bg\"></view>\n        </view>\n        <text class=\"action-text\">聊天</text>\n        <text class=\"action-desc\">甜蜜对话</text>\n      </view>\n      <view class=\"action-item\" @tap=\"goToAnniversary\">\n        <view class=\"action-icon-container\">\n          <text class=\"action-icon\">🎉</text>\n          <view class=\"icon-bg\"></view>\n        </view>\n        <text class=\"action-text\">纪念日</text>\n        <text class=\"action-desc\">重要时刻</text>\n      </view>\n      <view class=\"action-item\" @tap=\"goToDiary\">\n        <view class=\"action-icon-container\">\n          <text class=\"action-icon\">📝</text>\n          <view class=\"icon-bg\"></view>\n        </view>\n        <text class=\"action-text\">日记</text>\n        <text class=\"action-desc\">心情记录</text>\n      </view>\n    </view>\n\n    <!-- 今日心情 -->\n    <view class=\"mood-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">今日心情</text>\n        <text class=\"section-subtitle\">分享你的感受</text>\n      </view>\n      <view class=\"mood-list\">\n        <view class=\"mood-item\" v-for=\"(mood, index) in moods\" :key=\"index\" @tap=\"selectMood(mood)\" :class=\"{ 'selected': selectedMood === mood.name }\">\n          <view class=\"mood-emoji-container\">\n            <text class=\"mood-emoji\">{{ mood.emoji }}</text>\n          </view>\n          <text class=\"mood-name\">{{ mood.name }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 最近纪念日 -->\n    <view class=\"anniversary-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">即将到来</text>\n        <text class=\"section-subtitle\">不要错过重要日子</text>\n      </view>\n      <view class=\"anniversary-card\">\n        <view class=\"anniversary-icon\">🎂</view>\n        <view class=\"anniversary-info\">\n          <text class=\"anniversary-name\">我们的生日</text>\n          <text class=\"anniversary-date\">还有 15 天</text>\n          <view class=\"anniversary-progress\">\n            <view class=\"progress-bar\" style=\"width: 70%\"></view>\n          </view>\n        </view>\n        <view class=\"anniversary-arrow\">→</view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      loveDays: 365,\n      loveStartDate: '2023-08-04',\n      userAvatar: '/static/avatar1.png',\n      partnerAvatar: '/static/avatar2.png',\n      selectedMood: '',\n      dailyQuote: '爱情不是寻找一个完美的人，而是学会用完美的眼光欣赏不完美的人 💕',\n      moods: [\n        { emoji: '😊', name: '开心' },\n        { emoji: '😍', name: '甜蜜' },\n        { emoji: '🥰', name: '幸福' },\n        { emoji: '😌', name: '平静' },\n        { emoji: '😔', name: '想念' }\n      ]\n    };\n  },\n  methods: {\n    goToAlbum() {\n      uni.showToast({ title: '相册功能待开发', icon: 'none' });\n    },\n    goToChat() {\n      uni.showToast({ title: '聊天功能待开发', icon: 'none' });\n    },\n    goToAnniversary() {\n      uni.showToast({ title: '纪念日功能待开发', icon: 'none' });\n    },\n    goToDiary() {\n      uni.showToast({ title: '日记功能待开发', icon: 'none' });\n    },\n    selectMood(mood) {\n      this.selectedMood = mood.name;\n      uni.showToast({ title: `选择了${mood.name}心情`, icon: 'none' });\n    },\n    getParticleStyle(index) {\n      const delay = Math.random() * 3;\n      const duration = 3 + Math.random() * 2;\n      const left = Math.random() * 100;\n      return {\n        left: left + '%',\n        animationDelay: delay + 's',\n        animationDuration: duration + 's'\n      };\n    }\n  }\n};\n</script>\n\n<style scoped>\n.container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);\n  padding: 30rpx;\n  overflow: hidden;\n  position: relative;\n}\n\n/* 背景粒子动画 */\n.particles {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n  z-index: 1;\n}\n\n.particle {\n  position: absolute;\n  width: 8rpx;\n  height: 8rpx;\n  background: rgba(255, 255, 255, 0.6);\n  border-radius: 50%;\n  animation: float 5s infinite linear;\n}\n\n@keyframes float {\n  0% {\n    transform: translateY(100vh) rotate(0deg);\n    opacity: 0;\n  }\n  10% {\n    opacity: 1;\n  }\n  90% {\n    opacity: 1;\n  }\n  100% {\n    transform: translateY(-100rpx) rotate(360deg);\n    opacity: 0;\n  }\n}\n\n/* 每日情话 */\n.daily-quote {\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 25rpx;\n  padding: 25rpx 30rpx;\n  margin-bottom: 30rpx;\n  position: relative;\n  box-shadow: 0 8rpx 25rpx rgba(255, 105, 180, 0.3);\n  backdrop-filter: blur(10rpx);\n  animation: slideDown 0.6s ease-out;\n  z-index: 2;\n}\n\n.quote-text {\n  font-size: 26rpx;\n  color: #ff1493;\n  line-height: 1.5;\n  display: block;\n  margin-bottom: 10rpx;\n}\n\n.quote-icon {\n  position: absolute;\n  right: 20rpx;\n  top: 20rpx;\n  font-size: 24rpx;\n  animation: pulse 2s infinite;\n}\n\n/* 情侣信息卡片 */\n.couple-card {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 35rpx;\n  padding: 45rpx;\n  margin-bottom: 35rpx;\n  box-shadow: 0 15rpx 40rpx rgba(255, 105, 180, 0.4);\n  backdrop-filter: blur(15rpx);\n  animation: slideDown 0.8s ease-out;\n  position: relative;\n  z-index: 2;\n}\n\n.love-days {\n  text-align: center;\n  margin-bottom: 40rpx;\n}\n\n.days-number {\n  font-size: 88rpx;\n  font-weight: bold;\n  color: #ff1493;\n  display: block;\n  text-shadow: 0 2rpx 8rpx rgba(255, 20, 147, 0.3);\n}\n\n.days-text {\n  font-size: 28rpx;\n  color: #ff69b4;\n  margin-left: 10rpx;\n}\n\n.love-subtitle {\n  font-size: 22rpx;\n  color: #ff69b4;\n  margin-top: 10rpx;\n  display: block;\n  opacity: 0.8;\n}\n\n.couple-info {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 25rpx;\n}\n\n.user-avatar {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  flex: 1;\n}\n\n.avatar-container {\n  position: relative;\n  margin-bottom: 15rpx;\n}\n\n.avatar {\n  width: 130rpx;\n  height: 130rpx;\n  border-radius: 65rpx;\n  border: 3rpx solid #ff69b4;\n}\n\n.avatar-ring {\n  position: absolute;\n  top: -8rpx;\n  left: -8rpx;\n  width: 146rpx;\n  height: 146rpx;\n  border: 2rpx solid rgba(255, 105, 180, 0.3);\n  border-radius: 73rpx;\n  animation: rotate 3s linear infinite;\n}\n\n.username {\n  font-size: 26rpx;\n  color: #ff1493;\n  font-weight: 600;\n  margin-bottom: 5rpx;\n}\n\n.user-status {\n  font-size: 20rpx;\n  color: #ff69b4;\n  opacity: 0.7;\n}\n\n.heart-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  position: relative;\n}\n\n.heart-icon {\n  font-size: 65rpx;\n  animation: heartBeat 2s infinite;\n  z-index: 2;\n}\n\n.love-line {\n  position: absolute;\n  top: 50%;\n  left: -80rpx;\n  right: -80rpx;\n  height: 2rpx;\n  background: linear-gradient(90deg, transparent, #ff69b4, transparent);\n  z-index: 1;\n}\n\n.love-date {\n  text-align: center;\n  font-size: 24rpx;\n  color: #ff69b4;\n  opacity: 0.8;\n}\n\n/* 快捷功能区 */\n.quick-actions {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n  margin-bottom: 35rpx;\n  z-index: 2;\n  position: relative;\n}\n\n.action-item {\n  width: 48%;\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 25rpx;\n  padding: 35rpx 20rpx;\n  text-align: center;\n  margin-bottom: 20rpx;\n  box-shadow: 0 8rpx 25rpx rgba(255, 105, 180, 0.25);\n  backdrop-filter: blur(10rpx);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.action-item:active {\n  transform: scale(0.95);\n  box-shadow: 0 4rpx 15rpx rgba(255, 105, 180, 0.4);\n}\n\n.action-item::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\n  transition: left 0.5s ease;\n}\n\n.action-item:active::before {\n  left: 100%;\n}\n\n.action-icon-container {\n  position: relative;\n  margin-bottom: 15rpx;\n}\n\n.action-icon {\n  font-size: 52rpx;\n  position: relative;\n  z-index: 2;\n}\n\n.icon-bg {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 80rpx;\n  height: 80rpx;\n  background: linear-gradient(135deg, rgba(255, 105, 180, 0.1), rgba(255, 20, 147, 0.1));\n  border-radius: 50%;\n  z-index: 1;\n}\n\n.action-text {\n  font-size: 28rpx;\n  color: #ff1493;\n  font-weight: 600;\n  margin-bottom: 5rpx;\n  display: block;\n}\n\n.action-desc {\n  font-size: 20rpx;\n  color: #ff69b4;\n  opacity: 0.7;\n}\n\n/* 心情区域 */\n.mood-section {\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 25rpx;\n  padding: 35rpx;\n  margin-bottom: 35rpx;\n  backdrop-filter: blur(10rpx);\n  box-shadow: 0 8rpx 25rpx rgba(255, 105, 180, 0.2);\n  position: relative;\n  z-index: 2;\n}\n\n.section-header {\n  margin-bottom: 25rpx;\n}\n\n.section-title {\n  font-size: 34rpx;\n  color: #ff1493;\n  font-weight: bold;\n  margin-bottom: 8rpx;\n  display: block;\n}\n\n.section-subtitle {\n  font-size: 22rpx;\n  color: #ff69b4;\n  opacity: 0.7;\n}\n\n.mood-list {\n  display: flex;\n  justify-content: space-between;\n}\n\n.mood-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 20rpx 15rpx;\n  border-radius: 20rpx;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.mood-item:active {\n  transform: scale(0.95);\n}\n\n.mood-item.selected {\n  background: linear-gradient(135deg, #ff69b4, #ff1493);\n  color: white;\n}\n\n.mood-item.selected .mood-name {\n  color: white;\n}\n\n.mood-emoji-container {\n  margin-bottom: 10rpx;\n  position: relative;\n}\n\n.mood-emoji {\n  font-size: 40rpx;\n  transition: transform 0.3s ease;\n}\n\n.mood-item:active .mood-emoji {\n  transform: scale(1.2);\n}\n\n.mood-name {\n  font-size: 22rpx;\n  color: #ff69b4;\n  font-weight: 500;\n  transition: color 0.3s ease;\n}\n\n/* 纪念日区域 */\n.anniversary-section {\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 25rpx;\n  padding: 35rpx;\n  backdrop-filter: blur(10rpx);\n  box-shadow: 0 8rpx 25rpx rgba(255, 105, 180, 0.2);\n  position: relative;\n  z-index: 2;\n}\n\n.anniversary-card {\n  display: flex;\n  align-items: center;\n  background: linear-gradient(135deg, #ff69b4, #ff1493);\n  border-radius: 20rpx;\n  padding: 30rpx;\n  color: white;\n  box-shadow: 0 8rpx 20rpx rgba(255, 20, 147, 0.3);\n  transition: transform 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.anniversary-card:active {\n  transform: scale(0.98);\n}\n\n.anniversary-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  transition: left 0.6s ease;\n}\n\n.anniversary-card:active::before {\n  left: 100%;\n}\n\n.anniversary-icon {\n  font-size: 52rpx;\n  margin-right: 25rpx;\n  animation: bounce 2s infinite;\n}\n\n.anniversary-info {\n  flex: 1;\n}\n\n.anniversary-name {\n  font-size: 30rpx;\n  font-weight: bold;\n  display: block;\n  margin-bottom: 8rpx;\n}\n\n.anniversary-date {\n  font-size: 24rpx;\n  opacity: 0.9;\n  margin-bottom: 10rpx;\n}\n\n.anniversary-progress {\n  width: 100%;\n  height: 6rpx;\n  background: rgba(255, 255, 255, 0.3);\n  border-radius: 3rpx;\n  overflow: hidden;\n}\n\n.progress-bar {\n  height: 100%;\n  background: rgba(255, 255, 255, 0.8);\n  border-radius: 3rpx;\n  transition: width 0.3s ease;\n}\n\n.anniversary-arrow {\n  font-size: 24rpx;\n  margin-left: 15rpx;\n  opacity: 0.8;\n}\n\n/* 动画效果 */\n@keyframes slideDown {\n  0% {\n    transform: translateY(-30rpx);\n    opacity: 0;\n  }\n  100% {\n    transform: translateY(0);\n    opacity: 1;\n  }\n}\n\n@keyframes heartBeat {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.1);\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(1);\n    opacity: 1;\n  }\n  50% {\n    transform: scale(1.05);\n    opacity: 0.8;\n  }\n}\n\n@keyframes rotate {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes bounce {\n  0%, 20%, 50%, 80%, 100% {\n    transform: translateY(0);\n  }\n  40% {\n    transform: translateY(-10rpx);\n  }\n  60% {\n    transform: translateY(-5rpx);\n  }\n}\n</style>\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/auglar/text/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAuHA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,UAAU;AAAA,MACV,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,OAAO;AAAA,QACL,EAAE,OAAO,MAAM,MAAM,KAAM;AAAA,QAC3B,EAAE,OAAO,MAAM,MAAM,KAAM;AAAA,QAC3B,EAAE,OAAO,MAAM,MAAM,KAAM;AAAA,QAC3B,EAAE,OAAO,MAAM,MAAM,KAAM;AAAA,QAC3B,EAAE,OAAO,MAAM,MAAM,KAAK;AAAA,MAC5B;AAAA;EAEH;AAAA,EACD,SAAS;AAAA,IACP,YAAY;AACVA,oBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,OAAK,CAAG;AAAA,IACjD;AAAA,IACD,WAAW;AACTA,oBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,OAAK,CAAG;AAAA,IACjD;AAAA,IACD,kBAAkB;AAChBA,oBAAG,MAAC,UAAU,EAAE,OAAO,YAAY,MAAM,OAAK,CAAG;AAAA,IAClD;AAAA,IACD,YAAY;AACVA,oBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,OAAK,CAAG;AAAA,IACjD;AAAA,IACD,WAAW,MAAM;AACf,WAAK,eAAe,KAAK;AACzBA,oBAAAA,MAAI,UAAU,EAAE,OAAO,MAAM,KAAK,IAAI,MAAM,MAAM,OAAQ,CAAA;AAAA,IAC3D;AAAA,IACD,iBAAiB,OAAO;AACtB,YAAM,QAAQ,KAAK,OAAM,IAAK;AAC9B,YAAM,WAAW,IAAI,KAAK,OAAM,IAAK;AACrC,YAAM,OAAO,KAAK,OAAM,IAAK;AAC7B,aAAO;AAAA,QACL,MAAM,OAAO;AAAA,QACb,gBAAgB,QAAQ;AAAA,QACxB,mBAAmB,WAAW;AAAA;IAElC;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpKA,GAAG,WAAW,eAAe;"}