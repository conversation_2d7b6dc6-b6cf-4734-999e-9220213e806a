{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 情侣信息卡片 -->\n    <view class=\"couple-card\">\n      <view class=\"love-days\">\n        <text class=\"days-number\">{{ loveDays }}</text>\n        <text class=\"days-text\">天</text>\n      </view>\n      <view class=\"couple-info\">\n        <view class=\"user-avatar\">\n          <image :src=\"userAvatar\" class=\"avatar\"></image>\n          <text class=\"username\">小美</text>\n        </view>\n        <view class=\"heart-icon\">💞</view>\n        <view class=\"user-avatar\">\n          <image :src=\"partnerAvatar\" class=\"avatar\"></image>\n          <text class=\"username\">小帅</text>\n        </view>\n      </view>\n      <text class=\"love-date\">恋爱开始于 {{ loveStartDate }}</text>\n    </view>\n\n    <!-- 快捷功能区 -->\n    <view class=\"quick-actions\">\n      <view class=\"action-item\" @tap=\"goToAlbum\">\n        <view class=\"action-icon\">📷</view>\n        <text class=\"action-text\">相册</text>\n      </view>\n      <view class=\"action-item\" @tap=\"goToChat\">\n        <view class=\"action-icon\">💬</view>\n        <text class=\"action-text\">聊天</text>\n      </view>\n      <view class=\"action-item\" @tap=\"goToAnniversary\">\n        <view class=\"action-icon\">🎉</view>\n        <text class=\"action-text\">纪念日</text>\n      </view>\n      <view class=\"action-item\" @tap=\"goToDiary\">\n        <view class=\"action-icon\">📝</view>\n        <text class=\"action-text\">日记</text>\n      </view>\n    </view>\n\n    <!-- 今日心情 -->\n    <view class=\"mood-section\">\n      <text class=\"section-title\">今日心情</text>\n      <view class=\"mood-list\">\n        <view class=\"mood-item\" v-for=\"(mood, index) in moods\" :key=\"index\" @tap=\"selectMood(mood)\">\n          <text class=\"mood-emoji\">{{ mood.emoji }}</text>\n          <text class=\"mood-name\">{{ mood.name }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 最近纪念日 -->\n    <view class=\"anniversary-section\">\n      <text class=\"section-title\">即将到来</text>\n      <view class=\"anniversary-card\">\n        <view class=\"anniversary-icon\">🎂</view>\n        <view class=\"anniversary-info\">\n          <text class=\"anniversary-name\">我们的生日</text>\n          <text class=\"anniversary-date\">还有 15 天</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      loveDays: 365,\n      loveStartDate: '2023-08-04',\n      userAvatar: '/static/avatar1.png',\n      partnerAvatar: '/static/avatar2.png',\n      moods: [\n        { emoji: '😊', name: '开心' },\n        { emoji: '😍', name: '甜蜜' },\n        { emoji: '🥰', name: '幸福' },\n        { emoji: '😌', name: '平静' },\n        { emoji: '😔', name: '想念' }\n      ]\n    };\n  },\n  methods: {\n    goToAlbum() {\n      uni.showToast({ title: '相册功能待开发', icon: 'none' });\n    },\n    goToChat() {\n      uni.showToast({ title: '聊天功能待开发', icon: 'none' });\n    },\n    goToAnniversary() {\n      uni.showToast({ title: '纪念日功能待开发', icon: 'none' });\n    },\n    goToDiary() {\n      uni.showToast({ title: '日记功能待开发', icon: 'none' });\n    },\n    selectMood(mood) {\n      uni.showToast({ title: `选择了${mood.name}心情`, icon: 'none' });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #ffb3d9, #ffe6f2);\n  padding: 30rpx;\n  overflow: hidden;\n}\n\n/* 情侣信息卡片 */\n.couple-card {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 35rpx;\n  padding: 45rpx;\n  margin-bottom: 35rpx;\n  box-shadow: 0 10rpx 35rpx rgba(255, 105, 180, 0.4);\n  backdrop-filter: blur(12rpx);\n  animation: slideDown 0.8s ease-out;\n}\n\n.love-days {\n  text-align: center;\n  margin-bottom: 35rpx;\n}\n\n.days-number {\n  font-size: 80rpx;\n  font-weight: bold;\n  color: #ff1493;\n  display: block;\n}\n\n.days-text {\n  font-size: 24rpx;\n  color: #ff69b4;\n}\n\n.couple-info {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 20rpx;\n}\n\n.user-avatar {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.avatar {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 60rpx;\n  border: 4rpx solid #ff69b4;\n  margin-bottom: 10rpx;\n}\n\n.username {\n  font-size: 24rpx;\n  color: #ff1493;\n  font-weight: 600;\n}\n\n.heart-icon {\n  font-size: 60rpx;\n  animation: heartBeat 2s infinite;\n}\n\n.love-date {\n  text-align: center;\n  font-size: 24rpx;\n  color: #ff69b4;\n}\n\n/* 快捷功能区 */\n.quick-actions {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n  margin-bottom: 30rpx;\n}\n\n.action-item {\n  width: 48%;\n  background: rgba(255, 255, 255, 0.8);\n  border-radius: 20rpx;\n  padding: 30rpx 20rpx;\n  text-align: center;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 16rpx rgba(255, 105, 180, 0.2);\n  transition: transform 0.3s ease;\n}\n\n.action-item:active {\n  transform: scale(0.95);\n}\n\n.action-icon {\n  font-size: 48rpx;\n  margin-bottom: 10rpx;\n}\n\n.action-text {\n  font-size: 26rpx;\n  color: #ff1493;\n  font-weight: 600;\n}\n\n/* 心情区域 */\n.mood-section {\n  background: rgba(255, 255, 255, 0.8);\n  border-radius: 20rpx;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n  backdrop-filter: blur(5rpx);\n}\n\n.section-title {\n  font-size: 32rpx;\n  color: #ff1493;\n  font-weight: bold;\n  margin-bottom: 20rpx;\n  display: block;\n}\n\n.mood-list {\n  display: flex;\n  justify-content: space-between;\n}\n\n.mood-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 15rpx;\n  border-radius: 15rpx;\n  transition: background-color 0.3s ease;\n}\n\n.mood-item:active {\n  background-color: rgba(255, 105, 180, 0.1);\n}\n\n.mood-emoji {\n  font-size: 36rpx;\n  margin-bottom: 8rpx;\n}\n\n.mood-name {\n  font-size: 20rpx;\n  color: #ff69b4;\n}\n\n/* 纪念日区域 */\n.anniversary-section {\n  background: rgba(255, 255, 255, 0.8);\n  border-radius: 20rpx;\n  padding: 30rpx;\n  backdrop-filter: blur(5rpx);\n}\n\n.anniversary-card {\n  display: flex;\n  align-items: center;\n  background: linear-gradient(135deg, #ff69b4, #ff1493);\n  border-radius: 15rpx;\n  padding: 25rpx;\n  color: white;\n}\n\n.anniversary-icon {\n  font-size: 48rpx;\n  margin-right: 20rpx;\n}\n\n.anniversary-info {\n  flex: 1;\n}\n\n.anniversary-name {\n  font-size: 28rpx;\n  font-weight: bold;\n  display: block;\n  margin-bottom: 5rpx;\n}\n\n.anniversary-date {\n  font-size: 22rpx;\n  opacity: 0.9;\n}\n\n/* 动画效果 */\n@keyframes slideDown {\n  0% {\n    transform: translateY(-30rpx);\n    opacity: 0;\n  }\n  100% {\n    transform: translateY(0);\n    opacity: 1;\n  }\n}\n\n@keyframes heartBeat {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.1);\n  }\n}\n</style>\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/auglar/text/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAoEA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,UAAU;AAAA,MACV,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,OAAO;AAAA,QACL,EAAE,OAAO,MAAM,MAAM,KAAM;AAAA,QAC3B,EAAE,OAAO,MAAM,MAAM,KAAM;AAAA,QAC3B,EAAE,OAAO,MAAM,MAAM,KAAM;AAAA,QAC3B,EAAE,OAAO,MAAM,MAAM,KAAM;AAAA,QAC3B,EAAE,OAAO,MAAM,MAAM,KAAK;AAAA,MAC5B;AAAA;EAEH;AAAA,EACD,SAAS;AAAA,IACP,YAAY;AACVA,oBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,OAAK,CAAG;AAAA,IACjD;AAAA,IACD,WAAW;AACTA,oBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,OAAK,CAAG;AAAA,IACjD;AAAA,IACD,kBAAkB;AAChBA,oBAAG,MAAC,UAAU,EAAE,OAAO,YAAY,MAAM,OAAK,CAAG;AAAA,IAClD;AAAA,IACD,YAAY;AACVA,oBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,OAAK,CAAG;AAAA,IACjD;AAAA,IACD,WAAW,MAAM;AACfA,oBAAAA,MAAI,UAAU,EAAE,OAAO,MAAM,KAAK,IAAI,MAAM,MAAM,OAAQ,CAAA;AAAA,IAC5D;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;ACpGA,GAAG,WAAW,eAAe;"}