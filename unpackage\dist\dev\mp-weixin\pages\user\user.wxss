
body.data-v-0f7520f0 {
  font-family: "Arial", sans-serif;
  background: linear-gradient(135deg, #e1bee7, #f3e5f5);
  overflow: hidden;
}
.container.data-v-0f7520f0 {
  padding: 50rpx;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.profile-card.data-v-0f7520f0 {
  background: #fff;
  width: 95%;
  border-radius: 20rpx;
  text-align: center;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
  padding: 45rpx 25rpx;
  transform: scale(1);
  transition: transform 0.3s ease,
              box-shadow 0.3s ease;
}
.profile-card.data-v-0f7520f0:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}
.profile-avatar.data-v-0f7520f0 {
  width: 150rpx;
  height: 150rpx;
  border-radius: 75rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: box-shadow 0.3s ease;
}
.profile-avatar.data-v-0f7520f0:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}
.profile-name.data-v-0f7520f0 {
  font-size: 32rpx;
  color: #6a1b9a;
  margin-bottom: 25rpx;
}
.action-buttons.data-v-0f7520f0 {
  margin-top: 40rpx;
  width: 100%;
  display: flex;
  justify-content: space-evenly;
}
.action-buttons button.data-v-0f7520f0 {
  width: 45%;
  background: #ab47bc;
  color: #fff;
  border-radius: 30rpx;
  padding: 20rpx;
  border: none;
  font-size: 28rpx;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  transition: box-shadow 0.3s ease;
}
.action-buttons button.data-v-0f7520f0:active {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
