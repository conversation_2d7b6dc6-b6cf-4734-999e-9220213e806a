
.container.data-v-0f7520f0 {
		min-height: 100vh;
		background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
		padding: 30rpx;
		position: relative;
		overflow: hidden;
}

	/* 背景装饰 */
.bg-decoration.data-v-0f7520f0 {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		pointer-events: none;
		z-index: 1;
}
.decoration-circle.data-v-0f7520f0 {
		position: absolute;
		border-radius: 50%;
		background: rgba(255, 255, 255, 0.1);
		animation: float-0f7520f0 6s ease-in-out infinite;
}
.circle-1.data-v-0f7520f0 {
		width: 200rpx;
		height: 200rpx;
		top: 10%;
		right: -50rpx;
		animation-delay: 0s;
}
.circle-2.data-v-0f7520f0 {
		width: 150rpx;
		height: 150rpx;
		top: 60%;
		left: -30rpx;
		animation-delay: 2s;
}
.circle-3.data-v-0f7520f0 {
		width: 100rpx;
		height: 100rpx;
		top: 30%;
		left: 50%;
		animation-delay: 4s;
}
@keyframes float-0f7520f0 {
0%,
		100% {
			transform: translateY(0px) rotate(0deg);
}
50% {
			transform: translateY(-20rpx) rotate(180deg);
}
}

	/* 个人信息区域 */
.profile-section.data-v-0f7520f0 {
		position: relative;
		z-index: 2;
		margin-bottom: 30rpx;
}
.profile-card.data-v-0f7520f0 {
		background: rgba(255, 255, 255, 0.95);
		border-radius: 30rpx;
		padding: 40rpx;
		text-align: center;
		box-shadow: 0 15rpx 35rpx rgba(255, 105, 180, 0.3);
		-webkit-backdrop-filter: blur(15rpx);
		        backdrop-filter: blur(15rpx);
		animation: slideUp-0f7520f0 0.8s ease-out;
}
.avatar-container.data-v-0f7520f0 {
		position: relative;
		display: inline-block;
		margin-bottom: 25rpx;
}
.profile-avatar.data-v-0f7520f0 {
		width: 160rpx;
		height: 160rpx;
		border-radius: 80rpx;
		border: 4rpx solid #ff69b4;
		position: relative;
		z-index: 2;
}
.avatar-border.data-v-0f7520f0 {
		position: absolute;
		top: -10rpx;
		left: -10rpx;
		width: 180rpx;
		height: 180rpx;
		border: 2rpx solid rgba(255, 105, 180, 0.3);
		border-radius: 90rpx;
		animation: rotate-0f7520f0 4s linear infinite;
}
.online-status.data-v-0f7520f0 {
		position: absolute;
		bottom: 10rpx;
		right: 10rpx;
		width: 24rpx;
		height: 24rpx;
		background: #4caf50;
		border: 3rpx solid white;
		border-radius: 50%;
		z-index: 3;
}
.profile-info.data-v-0f7520f0 {
		margin-bottom: 25rpx;
}
.profile-name.data-v-0f7520f0 {
		font-size: 36rpx;
		color: #ff1493;
		font-weight: bold;
		margin-bottom: 10rpx;
		display: block;
}
.profile-subtitle.data-v-0f7520f0 {
		font-size: 24rpx;
		color: #ff69b4;
		opacity: 0.8;
}
.login-btn.data-v-0f7520f0 {
		background: linear-gradient(135deg, #ff69b4, #ff1493);
		border: none;
		border-radius: 25rpx;
		padding: 20rpx 40rpx;
		box-shadow: 0 8rpx 20rpx rgba(255, 20, 147, 0.3);
		transition: all 0.3s ease;
}
.login-btn.data-v-0f7520f0:active {
		transform: scale(0.95);
		box-shadow: 0 4rpx 15rpx rgba(255, 20, 147, 0.4);
}
.login-text.data-v-0f7520f0 {
		color: white;
		font-size: 28rpx;
		font-weight: 600;
}

	/* 情侣关系卡片 */
.relationship-card.data-v-0f7520f0 {
		background: rgba(255, 255, 255, 0.9);
		border-radius: 25rpx;
		padding: 35rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 10rpx 30rpx rgba(255, 105, 180, 0.25);
		-webkit-backdrop-filter: blur(10rpx);
		        backdrop-filter: blur(10rpx);
		position: relative;
		z-index: 2;
		animation: slideUp-0f7520f0 0.8s ease-out 0.2s both;
}
.relationship-header.data-v-0f7520f0 {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 25rpx;
}
.relationship-title.data-v-0f7520f0 {
		font-size: 30rpx;
		color: #ff1493;
		font-weight: bold;
}
.relationship-status.data-v-0f7520f0 {
		font-size: 22rpx;
		color: #4caf50;
		background: rgba(76, 175, 80, 0.1);
		padding: 8rpx 16rpx;
		border-radius: 15rpx;
}
.couple-avatars.data-v-0f7520f0 {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 20rpx;
}
.couple-item.data-v-0f7520f0 {
		display: flex;
		flex-direction: column;
		align-items: center;
		flex: 1;
}
.couple-avatar.data-v-0f7520f0 {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50rpx;
		border: 3rpx solid #ff69b4;
		margin-bottom: 10rpx;
}
.couple-name.data-v-0f7520f0 {
		font-size: 22rpx;
		color: #ff1493;
		font-weight: 600;
}
.heart-connector.data-v-0f7520f0 {
		font-size: 40rpx;
		animation: pulse-0f7520f0 2s infinite;
}
.relationship-days.data-v-0f7520f0 {
		text-align: center;
}
.days-text.data-v-0f7520f0 {
		font-size: 24rpx;
		color: #ff69b4;
		opacity: 0.8;
}

	/* 数据统计 */
.stats-section.data-v-0f7520f0 {
		background: rgba(255, 255, 255, 0.9);
		border-radius: 25rpx;
		padding: 35rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 10rpx 30rpx rgba(255, 105, 180, 0.25);
		-webkit-backdrop-filter: blur(10rpx);
		        backdrop-filter: blur(10rpx);
		position: relative;
		z-index: 2;
		animation: slideUp-0f7520f0 0.8s ease-out 0.4s both;
}
.section-title.data-v-0f7520f0 {
		font-size: 30rpx;
		color: #ff1493;
		font-weight: bold;
		margin-bottom: 25rpx;
		display: block;
}
.stats-grid.data-v-0f7520f0 {
		display: flex;
		justify-content: space-between;
}
.stat-item.data-v-0f7520f0 {
		display: flex;
		flex-direction: column;
		align-items: center;
		flex: 1;
		padding: 20rpx 10rpx;
		border-radius: 15rpx;
		transition: all 0.3s ease;
}
.stat-item.data-v-0f7520f0:active {
		background: rgba(255, 105, 180, 0.1);
		transform: scale(0.95);
}
.stat-number.data-v-0f7520f0 {
		font-size: 36rpx;
		color: #ff1493;
		font-weight: bold;
		margin-bottom: 8rpx;
}
.stat-label.data-v-0f7520f0 {
		font-size: 22rpx;
		color: #ff69b4;
		opacity: 0.8;
}

	/* 功能菜单 */
.menu-section.data-v-0f7520f0 {
		background: rgba(255, 255, 255, 0.9);
		border-radius: 25rpx;
		padding: 20rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 10rpx 30rpx rgba(255, 105, 180, 0.25);
		-webkit-backdrop-filter: blur(10rpx);
		        backdrop-filter: blur(10rpx);
		position: relative;
		z-index: 2;
		animation: slideUp-0f7520f0 0.8s ease-out 0.6s both;
}
.menu-item.data-v-0f7520f0 {
		display: flex;
		align-items: center;
		padding: 25rpx 20rpx;
		border-radius: 15rpx;
		margin-bottom: 10rpx;
		transition: all 0.3s ease;
		position: relative;
		overflow: hidden;
}
.menu-item.data-v-0f7520f0:last-child {
		margin-bottom: 0;
}
.menu-item.data-v-0f7520f0:active {
		background: rgba(255, 105, 180, 0.1);
		transform: scale(0.98);
}
.menu-item.data-v-0f7520f0::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
		transition: left 0.5s ease;
}
.menu-item.data-v-0f7520f0:active::before {
		left: 100%;
}
.menu-icon.data-v-0f7520f0 {
		font-size: 40rpx;
		margin-right: 20rpx;
		width: 60rpx;
		text-align: center;
}
.menu-content.data-v-0f7520f0 {
		flex: 1;
}
.menu-title.data-v-0f7520f0 {
		font-size: 28rpx;
		color: #ff1493;
		font-weight: 600;
		margin-bottom: 5rpx;
		display: block;
}
.menu-desc.data-v-0f7520f0 {
		font-size: 22rpx;
		color: #ff69b4;
		opacity: 0.7;
}
.menu-arrow.data-v-0f7520f0 {
		font-size: 24rpx;
		color: #ff69b4;
		opacity: 0.6;
}

	/* 退出登录 */
.logout-section.data-v-0f7520f0 {
		position: relative;
		z-index: 2;
		animation: slideUp-0f7520f0 0.8s ease-out 0.8s both;
}
.logout-btn.data-v-0f7520f0 {
		width: 100%;
		background: rgba(255, 255, 255, 0.9);
		border: 2rpx solid #ff69b4;
		border-radius: 25rpx;
		padding: 25rpx;
		box-shadow: 0 8rpx 20rpx rgba(255, 105, 180, 0.2);
		-webkit-backdrop-filter: blur(10rpx);
		        backdrop-filter: blur(10rpx);
		transition: all 0.3s ease;
}
.logout-btn.data-v-0f7520f0:active {
		transform: scale(0.98);
		background: rgba(255, 105, 180, 0.1);
}
.logout-text.data-v-0f7520f0 {
		color: #ff1493;
		font-size: 28rpx;
		font-weight: 600;
}

	/* 动画效果 */
@keyframes slideUp-0f7520f0 {
0% {
			transform: translateY(50rpx);
			opacity: 0;
}
100% {
			transform: translateY(0);
			opacity: 1;
}
}
@keyframes rotate-0f7520f0 {
0% {
			transform: rotate(0deg);
}
100% {
			transform: rotate(360deg);
}
}
@keyframes pulse-0f7520f0 {
0%,
		100% {
			transform: scale(1);
}
50% {
			transform: scale(1.1);
}
}
